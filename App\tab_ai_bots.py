import customtkinter as ctk
import tkinter as tk
from tkinter import filedialog
from datetime import datetime, timedelta
import uuid
import os
import re
import threading
import time
import json

# ===========================
# Class: TabAIBots
# ===========================
class TabAIBots:
    def __init__(self, master, config, util):
        self.frame = master.add("AI Bots")
        self.config = config
        self.util = util

        # AI Bot Scheduler
        self.scheduler_thread = None
        self.scheduler_running = False
        self.bot_schedules = {}  # Track individual bot schedules

        # Create main container with scrollable frame
        self.main_container = ctk.CTkScrollableFrame(self.frame)
        self.main_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Header section
        # self.create_header()
        
        # Bot list section
        self.create_bot_list()
        
        # Add new bot section
        self.create_add_bot_section()
        
        # Refresh the bot list
        self.refresh_bot_list()

        # Start the AI bot scheduler
        self.start_scheduler()

    def estimate_tokens(self, text):
        """Estimate token count for text (rough approximation)"""
        if not text:
            return 0

        # Simple token estimation:
        # - Split by whitespace and punctuation
        # - Average ~4 characters per token for English text
        # - Add some buffer for special tokens

        # Remove extra whitespace and split
        words = re.split(r'\s+', text.strip())

        # Count characters and estimate tokens
        char_count = len(text)
        word_count = len(words)

        # More accurate estimation:
        # - Short words (1-3 chars) = 1 token
        # - Medium words (4-8 chars) = 1 token
        # - Long words (9+ chars) = 1-2 tokens
        # - Punctuation and special chars add tokens

        estimated_tokens = 0
        for word in words:
            if len(word) <= 3:
                estimated_tokens += 1
            elif len(word) <= 8:
                estimated_tokens += 1
            else:
                estimated_tokens += 2

        # Add buffer for punctuation, formatting, special tokens
        punctuation_count = len(re.findall(r'[^\w\s]', text))
        estimated_tokens += punctuation_count // 2

        # Add system message overhead (roughly 20-50 tokens)
        estimated_tokens += 30

        return max(estimated_tokens, char_count // 4)  # Fallback: ~4 chars per token
    
    def create_header(self):
        """Create header section with title and controls"""
        header_frame = ctk.CTkFrame(self.main_container)
        header_frame.pack(fill="x", pady=(0, 20))
        
        # Title
        title_label = ctk.CTkLabel(header_frame, text="AI Trading Bots", 
                                  font=ctk.CTkFont(size=20, weight="bold"))
        title_label.pack(pady=10)
        
        # Description
        desc_label = ctk.CTkLabel(header_frame, 
                                 text="Manage AI bots with different symbols, timeframes, and custom prompts",
                                 font=ctk.CTkFont(size=12))
        desc_label.pack(pady=(0, 10))
        
        # Control buttons
        control_frame = ctk.CTkFrame(header_frame)
        control_frame.pack(pady=10)
        
        refresh_btn = ctk.CTkButton(control_frame, text="🔄 Refresh", 
                                   command=self.refresh_bot_list, width=100)
        refresh_btn.pack(side="left", padx=5)
        
        test_all_btn = ctk.CTkButton(control_frame, text="🧪 Test All Enabled", 
                                    command=self.test_all_bots, width=120)
        test_all_btn.pack(side="left", padx=5)
    
    def create_bot_list(self):
        """Create scrollable list of AI bots"""
        # Bot list header
        list_header = ctk.CTkLabel(self.main_container, text="Current AI Bots", 
                                  font=ctk.CTkFont(size=16, weight="bold"))
        list_header.pack(anchor="w", pady=(10, 5))
        
        # Container for bot cards
        self.bot_list_frame = ctk.CTkFrame(self.main_container)
        self.bot_list_frame.pack(fill="x", pady=(0, 20))
    
    def create_add_bot_section(self):
        """Create section for adding new bots"""
        # Add bot header
        add_header = ctk.CTkLabel(self.main_container, text="Add New AI Bot", 
                                 font=ctk.CTkFont(size=16, weight="bold"))
        add_header.pack(anchor="w", pady=(20, 5))
        
        # Add bot form
        self.add_bot_frame = ctk.CTkFrame(self.main_container)
        self.add_bot_frame.pack(fill="x", pady=(0, 10))
        
        # Form fields
        form_grid = ctk.CTkFrame(self.add_bot_frame)
        form_grid.pack(fill="x", padx=20, pady=20)
        
        # Bot Name
        ctk.CTkLabel(form_grid, text="Bot Name:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.new_bot_name = ctk.CTkEntry(form_grid, placeholder_text="e.g., GBPUSD Scalper")
        self.new_bot_name.grid(row=0, column=1, sticky="ew", padx=5, pady=5)
        
        # Symbol
        ctk.CTkLabel(form_grid, text="Symbol:").grid(row=1, column=0, sticky="w", padx=5, pady=5)
        self.new_bot_symbol = ctk.CTkOptionMenu(form_grid, values=list(self.config.symbols.keys()))
        self.new_bot_symbol.grid(row=1, column=1, sticky="ew", padx=5, pady=5)
        self.new_bot_symbol.set(self.config.ai_bot_defaults["symbol"])
        
        # Timeframes (Multi-timeframe support)
        ctk.CTkLabel(form_grid, text="Timeframes:").grid(row=2, column=0, sticky="nw", padx=5, pady=5)
        timeframe_frame = ctk.CTkFrame(form_grid)
        timeframe_frame.grid(row=2, column=1, sticky="ew", padx=5, pady=5)

        # Multi-timeframe selection with checkboxes
        self.timeframe_vars = {}
        self.timeframe_checkboxes = {}

        # Create checkboxes for each timeframe
        available_timeframes = list(self.config.timeframes.keys())
        for i, tf in enumerate(available_timeframes):
            var = ctk.BooleanVar()
            checkbox = ctk.CTkCheckBox(timeframe_frame, text=tf, variable=var)

            # Arrange in rows of 4
            row = i // 4
            col = i % 4
            checkbox.grid(row=row, column=col, sticky="w", padx=5, pady=2)

            self.timeframe_vars[tf] = var
            self.timeframe_checkboxes[tf] = checkbox

        # Set default timeframe (H1)
        if "H1" in self.timeframe_vars:
            self.timeframe_vars["H1"].set(True)
        
        # Bars Back
        ctk.CTkLabel(form_grid, text="Bars Back:").grid(row=3, column=0, sticky="w", padx=5, pady=5)
        self.new_bot_bars = ctk.CTkEntry(form_grid, placeholder_text="100")
        self.new_bot_bars.grid(row=3, column=1, sticky="ew", padx=5, pady=5)
        self.new_bot_bars.insert(0, str(self.config.ai_bot_defaults["bars_back"]))
        
        # Custom Prompt
        ctk.CTkLabel(form_grid, text="Custom Prompt:").grid(row=4, column=0, sticky="nw", padx=5, pady=5)
        self.new_bot_prompt = ctk.CTkTextbox(form_grid, height=80)
        self.new_bot_prompt.grid(row=4, column=1, sticky="ew", padx=5, pady=5)
        self.new_bot_prompt.insert("1.0", self.config.ai_bot_defaults["prompt"])

        # AI API Provider
        ctk.CTkLabel(form_grid, text="AI Provider:").grid(row=5, column=0, sticky="w", padx=5, pady=5)
        self.new_bot_api = ctk.CTkOptionMenu(form_grid, values=["gpt", "gemini"])
        self.new_bot_api.grid(row=5, column=1, sticky="ew", padx=5, pady=5)
        self.new_bot_api.set(self.config.ai_bot_defaults["api_provider"])

        # Auto Place Order Toggle
        ctk.CTkLabel(form_grid, text="Auto Place Order:").grid(row=6, column=0, sticky="w", padx=5, pady=5)
        self.new_bot_auto_order = ctk.CTkSwitch(form_grid, text="Enable")
        self.new_bot_auto_order.grid(row=6, column=1, sticky="w", padx=5, pady=5)

        # Schedule Check Toggle
        ctk.CTkLabel(form_grid, text="Schedule Check:").grid(row=7, column=0, sticky="w", padx=5, pady=5)
        schedule_frame = ctk.CTkFrame(form_grid)
        schedule_frame.grid(row=7, column=1, sticky="ew", padx=5, pady=5)

        self.new_bot_schedule = ctk.CTkSwitch(schedule_frame, text="Enable")
        self.new_bot_schedule.pack(side="left")

        ctk.CTkLabel(schedule_frame, text="Interval (min):").pack(side="left", padx=(10, 5))
        self.new_bot_interval = ctk.CTkEntry(schedule_frame, width=60)
        self.new_bot_interval.pack(side="left")
        self.new_bot_interval.insert(0, str(self.config.ai_bot_defaults["check_interval"]))

        # Chart Image Upload
        ctk.CTkLabel(form_grid, text="Chart Image:").grid(row=8, column=0, sticky="w", padx=5, pady=5)
        image_frame = ctk.CTkFrame(form_grid)
        image_frame.grid(row=8, column=1, sticky="ew", padx=5, pady=5)

        self.new_bot_use_image = ctk.CTkSwitch(image_frame, text="Use Image")
        self.new_bot_use_image.pack(side="left")

        self.new_bot_image_btn = ctk.CTkButton(image_frame, text="📁 Select Image",
                                              command=self.select_chart_image, width=100)
        self.new_bot_image_btn.pack(side="left", padx=(10, 0))

        self.selected_image_path = ""

        # Signal Format Toggle (enabled by default)
        ctk.CTkLabel(form_grid, text="Signal Format:").grid(row=9, column=0, sticky="w", padx=5, pady=5)
        signal_format_frame = ctk.CTkFrame(form_grid)
        signal_format_frame.grid(row=9, column=1, sticky="ew", padx=5, pady=5)

        self.new_bot_signal_format = ctk.CTkSwitch(signal_format_frame, text="Use Structured Signal Format")
        self.new_bot_signal_format.pack(side="left")
        self.new_bot_signal_format.select()  # Default to enabled

        # Info label
        info_label = ctk.CTkLabel(signal_format_frame, text="(If disabled: bullet points format)",
                                 font=ctk.CTkFont(size=10), text_color="gray")
        info_label.pack(side="left", padx=(10, 0))

        # Configure grid weights
        form_grid.grid_columnconfigure(1, weight=1)
        
        # Add button
        add_btn_frame = ctk.CTkFrame(self.add_bot_frame)
        add_btn_frame.pack(pady=10)
        
        add_btn = ctk.CTkButton(add_btn_frame, text="➕ Add AI Bot",
                               command=self.add_new_bot, width=120)
        add_btn.pack()

    def select_chart_image(self):
        """Select chart image file for AI analysis"""
        file_types = [
            ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"),
            ("PNG files", "*.png"),
            ("JPEG files", "*.jpg *.jpeg"),
            ("All files", "*.*")
        ]

        filename = filedialog.askopenfilename(
            title="Select Chart Image",
            filetypes=file_types
        )

        if filename:
            self.selected_image_path = filename
            # Update button text to show selected file
            file_name = os.path.basename(filename)
            if len(file_name) > 15:
                file_name = file_name[:12] + "..."
            self.new_bot_image_btn.configure(text=f"📁 {file_name}")
            self.util.add_status_frame(f"📷 Selected chart image: {filename}", "cyan", level=4)
    
    def refresh_bot_list(self):
        """Refresh the display of AI bots"""
        try:
            # Use after_idle to ensure we're in the main thread
            self.frame.after_idle(self._do_refresh_bot_list)
        except Exception as e:
            print(f"Refresh scheduling error: {e}")
            # Fallback to direct refresh
            self._do_refresh_bot_list()

    def _do_refresh_bot_list(self):
        """Internal method to actually refresh the bot list"""
        try:
            # Clear existing bot cards
            for widget in self.bot_list_frame.winfo_children():
                widget.destroy()

            if not self.config.ai_bots:
                # Show empty state
                empty_label = ctk.CTkLabel(self.bot_list_frame,
                                          text="No AI bots configured. Add one below!",
                                          font=ctk.CTkFont(size=12))
                empty_label.pack(pady=20)
                return

            # Create bot cards
            for bot_id, bot_config in self.config.ai_bots.items():
                self.create_bot_card(bot_id, bot_config)

        except Exception as e:
            print(f"Error refreshing bot list: {e}")
            # Show error message
            error_label = ctk.CTkLabel(self.bot_list_frame,
                                      text=f"Error loading bots: {str(e)}",
                                      font=ctk.CTkFont(size=12), text_color="red")
            error_label.pack(pady=20)
    
    def create_bot_card(self, bot_id, bot_config):
        """Create a card for displaying bot information"""
        card = ctk.CTkFrame(self.bot_list_frame)
        card.pack(fill="x", padx=10, pady=5)
        
        # Main info frame
        info_frame = ctk.CTkFrame(card)
        info_frame.pack(fill="x", padx=10, pady=10)
        
        # Left side - Bot info
        left_frame = ctk.CTkFrame(info_frame)
        left_frame.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        # Bot name and status
        name_frame = ctk.CTkFrame(left_frame)
        name_frame.pack(fill="x", padx=10, pady=5)
        
        status_color = "green" if bot_config.get("enabled", True) else "gray"
        status_text = "🟢 ENABLED" if bot_config.get("enabled", True) else "🔴 DISABLED"
        
        name_label = ctk.CTkLabel(name_frame, text=bot_config.get("name", f"Bot {bot_id}"),
                                 font=ctk.CTkFont(size=14, weight="bold"))
        name_label.pack(side="left")
        
        status_label = ctk.CTkLabel(name_frame, text=status_text, text_color=status_color,
                                   font=ctk.CTkFont(size=10))
        status_label.pack(side="right")
        
        # Bot parameters
        params_frame = ctk.CTkFrame(left_frame)
        params_frame.pack(fill="x", padx=10, pady=5)

        symbol_full = self.config.symbols.get(bot_config.get("symbol", "EU"), "EURUSD")

        # Handle both multi-timeframe and single timeframe display
        timeframes = bot_config.get("timeframes", [bot_config.get("timeframe", "H1")])
        if isinstance(timeframes, list):
            timeframes_str = ", ".join(timeframes)
        else:
            timeframes_str = str(timeframes)

        params_text = f"📊 {bot_config.get('symbol', 'EU')} ({symbol_full}) | ⏰ {timeframes_str} | 📈 {bot_config.get('bars_back', 100)} bars"

        params_label = ctk.CTkLabel(params_frame, text=params_text, font=ctk.CTkFont(size=11))
        params_label.pack(anchor="w")
        
        # Custom prompt preview
        prompt_preview = bot_config.get("prompt", "")[:60] + "..." if len(bot_config.get("prompt", "")) > 60 else bot_config.get("prompt", "")
        prompt_label = ctk.CTkLabel(params_frame, text=f"💬 {prompt_preview}",
                                   font=ctk.CTkFont(size=10), text_color="gray")
        prompt_label.pack(anchor="w")

        # Additional features display
        features = []
        api_provider = bot_config.get("api_provider", "gpt").upper()
        features.append(f"🤖 {api_provider}")

        if bot_config.get("auto_place_order", False):
            features.append("🎯 Auto Order")

        if bot_config.get("schedule_check", False):
            interval = bot_config.get("check_interval", 60)
            features.append(f"⏰ {interval}min")

        if bot_config.get("use_chart_image", False):
            features.append("📷 Image")

        if bot_config.get("use_signal_format", False):
            features.append("📋 Signal Format")

        if bot_config.get("multi_timeframe", False):
            features.append("📊 Multi-TF")

        if features:
            features_text = " | ".join(features)
            features_label = ctk.CTkLabel(params_frame, text=features_text,
                                         font=ctk.CTkFont(size=9), text_color="lightblue")
            features_label.pack(anchor="w")
        
        # Right side - Action buttons
        right_frame = ctk.CTkFrame(info_frame)
        right_frame.pack(side="right", padx=10, pady=5)
        
        # Toggle button
        toggle_text = "Disable" if bot_config.get("enabled", True) else "Enable"
        toggle_btn = ctk.CTkButton(right_frame, text=toggle_text, width=80,
                                  command=lambda: self.toggle_bot(bot_id))
        toggle_btn.pack(pady=2)
        
        # Test button
        # test_btn = ctk.CTkButton(right_frame, text="🧪 Test", width=80,
        #                         command=lambda: self.test_bot(bot_id))
        # test_btn.pack(pady=2)

        # Prompt button
        # prompt_btn = ctk.CTkButton(right_frame, text="📝 Prompt", width=80,
        #                           fg_color="orange", hover_color="darkorange",
        #                           command=lambda: self.show_prompt_popup(bot_id))
        # prompt_btn.pack(pady=2)

        # Analyze button (Real AI Analysis)
        analyze_btn = ctk.CTkButton(right_frame, text="🤖 Analyze", width=80,
                                   fg_color="purple", hover_color="darkviolet",
                                   command=lambda: self.analyze_bot(bot_id))
        analyze_btn.pack(pady=2)

        # Edit button
        edit_btn = ctk.CTkButton(right_frame, text="✏️ Edit", width=80,
                                command=lambda: self.edit_bot(bot_id))
        edit_btn.pack(pady=2)

        # Delete button
        delete_btn = ctk.CTkButton(right_frame, text="🗑️ Delete", width=80,
                                  fg_color="red", hover_color="darkred",
                                  command=lambda: self.delete_bot(bot_id))
        delete_btn.pack(pady=2)

    def add_new_bot(self):
        """Add a new AI bot"""
        try:
            # Validate inputs
            name = self.new_bot_name.get().strip()
            if not name:
                self.util.add_status_frame("❌ Bot name is required", "red")
                return

            symbol = self.new_bot_symbol.get()

            # Get selected timeframes
            selected_timeframes = []
            for tf, var in self.timeframe_vars.items():
                if var.get():
                    selected_timeframes.append(tf)

            if not selected_timeframes:
                self.util.add_status_frame("❌ At least one timeframe must be selected", "red", level=2)
                return

            try:
                bars_back = int(self.new_bot_bars.get())
                if bars_back <= 0:
                    raise ValueError("Bars back must be positive")
            except ValueError:
                self.util.add_status_frame("❌ Invalid bars back value", "red", level=2)
                return

            prompt = self.new_bot_prompt.get("1.0", "end-1c").strip()
            if not prompt:
                self.util.add_status_frame("❌ Custom prompt is required", "red", level=2)
                return

            # Get additional configuration
            api_provider = self.new_bot_api.get()
            auto_place_order = self.new_bot_auto_order.get()
            schedule_check = self.new_bot_schedule.get()

            try:
                check_interval = int(self.new_bot_interval.get()) if schedule_check else 60
                if check_interval <= 0:
                    raise ValueError("Check interval must be positive")
            except ValueError:
                self.util.add_status_frame("❌ Invalid check interval value", "red", level=2)
                return

            use_chart_image = self.new_bot_use_image.get()
            chart_image_path = self.selected_image_path if use_chart_image else ""
            use_signal_format = self.new_bot_signal_format.get()

            # Generate unique bot ID
            bot_id = f"bot_{uuid.uuid4().hex[:8]}"

            # Create bot configuration
            bot_config = {
                "name": name,
                "symbol": symbol,
                "timeframes": selected_timeframes,  # Multi-timeframe support
                "timeframe": selected_timeframes[0] if selected_timeframes else "H1",  # Legacy support
                "bars_back": bars_back,
                "prompt": prompt,
                "enabled": True,
                "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "last_used": None,
                "api_provider": api_provider,
                "auto_place_order": auto_place_order,
                "schedule_check": schedule_check,
                "check_interval": check_interval,
                "chart_image_path": chart_image_path,
                "use_chart_image": use_chart_image,
                "use_signal_format": use_signal_format,
                "multi_timeframe": len(selected_timeframes) > 1
            }

            # Add to config using JSON storage
            self.config.add_ai_bot(bot_id, bot_config)

            # Clear form
            self.new_bot_name.delete(0, "end")
            self.new_bot_bars.delete(0, "end")
            self.new_bot_bars.insert(0, str(self.config.ai_bot_defaults["bars_back"]))
            self.new_bot_prompt.delete("1.0", "end")
            self.new_bot_prompt.insert("1.0", self.config.ai_bot_defaults["prompt"])
            self.new_bot_api.set(self.config.ai_bot_defaults["api_provider"])
            self.new_bot_auto_order.deselect()
            self.new_bot_schedule.deselect()
            self.new_bot_interval.delete(0, "end")
            self.new_bot_interval.insert(0, str(self.config.ai_bot_defaults["check_interval"]))
            self.new_bot_use_image.deselect()
            self.new_bot_image_btn.configure(text="📁 Select Image")
            self.new_bot_signal_format.select()
            self.selected_image_path = ""

            # Clear timeframe selections and set default
            for tf, var in self.timeframe_vars.items():
                var.set(False)
            if "H1" in self.timeframe_vars:
                self.timeframe_vars["H1"].set(True)

            # Refresh display
            self.refresh_bot_list()

            symbol_full = self.config.symbols.get(symbol, symbol)
            timeframes_str = ", ".join(selected_timeframes)
            self.util.add_status_frame(f"✅ Added AI bot: {name} ({symbol_full} {timeframes_str})", "green", level=3)

        except Exception as e:
            self.util.add_status_frame(f"❌ Error adding bot: {e}", "red")

    def toggle_bot(self, bot_id):
        """Toggle bot enabled/disabled status"""
        if bot_id in self.config.ai_bots:
            current_status = self.config.ai_bots[bot_id].get("enabled", True)
            self.config.ai_bots[bot_id]["enabled"] = not current_status

            # Save changes to JSON file
            self.config.save_ai_bots()

            status_text = "enabled" if not current_status else "disabled"
            bot_name = self.config.ai_bots[bot_id].get("name", f"Bot {bot_id}")

            self.util.add_status_frame(f"🔄 AI bot {bot_name} {status_text}", "cyan")
            self.refresh_bot_list()

    def test_bot(self, bot_id):
        """Test a specific AI bot by sending chart data request"""
        if bot_id not in self.config.ai_bots:
            self.util.add_status_frame(f"❌ Bot {bot_id} not found", "red")
            return

        bot_config = self.config.ai_bots[bot_id]

        # Update last used timestamp
        self.config.ai_bots[bot_id]["last_used"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.config.save_ai_bots()

        # Get full symbol name
        symbol_key = bot_config.get("symbol")
        # symbol_full = self.config.symbols.get(symbol_key)
        symbol_full = self.util.get_symbol(symbol_key)

        # Get timeframes (support both multi-timeframe and single timeframe)
        timeframes = bot_config.get("timeframes", [bot_config.get("timeframe", "H1")])
        if not isinstance(timeframes, list):
            timeframes = [timeframes]

        # Prepare test data
        test_data = {
            "symbol": symbol_full,
            "timeframes": timeframes,
            "barback": bot_config.get("bars_back", 100),
            "bot_id": bot_id,
            "bot_name": bot_config.get("name", f"Bot {bot_id}"),
            "custom_prompt": bot_config.get("prompt", ""),
            "ai": bot_config.get("api_provider", "gpt"),
            "use_signal_format": bot_config.get("use_signal_format", False)
        }

        bot_name = bot_config.get("name", f"Bot {bot_id}")

        if hasattr(self.config, 'quiet_mode') and self.config.quiet_mode:
            self.util.add_status_frame(f"🧪 Testing {bot_name}", "yellow")
        else:
            self.util.add_status_frame(f"🧪 Testing AI bot: {bot_name} ({symbol_full} {test_data['timeframe']} {test_data['barback']}bars)", "yellow")

        # Here you would integrate with your webhook system to send the request
        # For now, just simulate the test
        self.simulate_bot_test(test_data)

        self.refresh_bot_list()

    def simulate_bot_test(self, test_data):
        """Simulate bot test (replace with actual webhook call)"""
        import time

        # Simulate processing delay
        self.util.add_status_frame(f"📊 Fetching {test_data['symbol']} data...", "cyan")

        # In a real implementation, you would:
        # 1. Call the webhook_chart_data endpoint with test_data
        # 2. Send the response to your AI service with the custom prompt
        # 3. Display the AI analysis results

        # For now, just show success
        self.util.add_status_frame(f"✅ Bot test completed: {test_data['bot_name']}", "green")

    def analyze_bot(self, bot_id):
        """Perform real AI analysis for the bot"""
        if bot_id not in self.config.ai_bots:
            self.util.add_status_frame(f"❌ Bot {bot_id} not found", "red", level=2)
            return

        bot_config = self.config.ai_bots[bot_id]

        try:
            # Get full symbol name
            symbol_key = bot_config.get("symbol")
            # symbol_full = self.config.symbols.get(symbol_key, "EURUSD")
            symbol_full = self.util.get_symbol(symbol_key)

            # Get timeframes (support both multi-timeframe and single timeframe)
            timeframes = bot_config.get("timeframes", [bot_config.get("timeframe", "H1")])
            if not isinstance(timeframes, list):
                timeframes = [timeframes]

            # Prepare analysis parameters
            barback = bot_config.get("bars_back", 100)
            custom_prompt = bot_config.get("prompt", "")
            ai_provider = bot_config.get("api_provider", "gpt")
            use_signal_format = bot_config.get("use_signal_format", False)
            image_url = ""  # Could be extended to support image URLs

            bot_name = bot_config.get("name", f"Bot {bot_id}")

            self.util.add_status_frame(f"📊 Preparing AI analysis for: {bot_name} ({symbol_full} {timeframes})", "cyan", level=3)

            # Get multi-timeframe chart data
            chart_data_result = self.util.get_multi_timeframe_data(symbol_full, timeframes, barback)
            if not chart_data_result:
                self.util.add_status_frame(f"❌ Failed to get chart data for {bot_name}", "red", level=2)
                return

            # Show confirmation popup (analysis will be performed when user clicks "Analyze Now")
            self.show_analysis_confirmation_popup(bot_id, bot_name, chart_data_result, custom_prompt, ai_provider, symbol_full, timeframes, use_signal_format)

        except Exception as e:
            self.util.add_status_frame(f"❌ Analysis error for {bot_name}: {str(e)}", "red", level=1)
 
    def show_prompt_popup(self, bot_id):
        """Show AI bot prompt in a popup window"""
        if bot_id not in self.config.ai_bots:
            self.util.add_status_frame(f"❌ Bot {bot_id} not found", "red", level=2)
            return

        bot_config = self.config.ai_bots[bot_id]
        bot_name = bot_config.get("name", f"Bot {bot_id}")

        # Prepare analysis parameters
        barback = bot_config.get("bars_back", 100)
        custom_prompt = bot_config.get("prompt", "")
        ai_provider = bot_config.get("api_provider", "gpt")
        use_signal_format = bot_config.get("use_signal_format", False)
        # image_url = ""  # Could be extended to support image URLs

        # Create popup dialog
        popup = ctk.CTkToplevel()
        popup.title(f"AI Bot Prompt - {bot_name}")
        popup.geometry("800x600")
        popup.transient(self.frame.winfo_toplevel())
        popup.grab_set()

        # Center the popup
        popup.update_idletasks()
        x = (popup.winfo_screenwidth() // 2) - (800 // 2)
        y = (popup.winfo_screenheight() // 2) - (600 // 2)
        popup.geometry(f"800x600+{x}+{y}")

        # Main content frame
        main_frame = ctk.CTkFrame(popup)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Header
        header_frame = ctk.CTkFrame(main_frame)
        header_frame.pack(fill="x", pady=(0, 10))

        title_label = ctk.CTkLabel(header_frame, text=f"📝 AI Bot Prompt",
                                  font=ctk.CTkFont(size=18, weight="bold"))
        title_label.pack(pady=10)

        # Bot info
        info_frame = ctk.CTkFrame(main_frame)
        info_frame.pack(fill="x", pady=(0, 10))

        symbol_key = bot_config.get("symbol", "EU")
        symbol_full = self.util.get_symbol(symbol_key)
        timeframes = bot_config.get("timeframes", [bot_config.get("timeframe", "H1")])
        if not isinstance(timeframes, list):
            timeframes = [timeframes]
        ai_provider = bot_config.get("api_provider", "gpt")

        info_text = f"Bot: {bot_name} | Symbol: {symbol_full} | Timeframes: {', '.join(timeframes)} | AI: {ai_provider.upper()}"
        info_label = ctk.CTkLabel(info_frame, text=info_text, font=ctk.CTkFont(size=12))
        info_label.pack(pady=5)

        # Get multi-timeframe chart data
        chart_data_result = self.util.get_multi_timeframe_data(symbol_full, timeframes, barback)
        if not chart_data_result:
            self.util.add_status_frame(f"❌ Failed to get chart data for {bot_name}", "red", level=2)
            return

        base_prompt = self.util.generate_prompt(chart_data_result, custom_prompt, symbol_full, timeframes, use_signal_format)

        # Token count info
        prompt_tokens = self.estimate_tokens(base_prompt)
        estimated_response_tokens = min(prompt_tokens // 2, 1000)  # Response usually shorter, max ~1000
        total_estimated = prompt_tokens + estimated_response_tokens

        token_info_text = f"📊 Tokens: Prompt ~{prompt_tokens:,} | Est. Response ~{estimated_response_tokens:,} | Total ~{total_estimated:,}"
        token_label = ctk.CTkLabel(info_frame, text=token_info_text,
                                  font=ctk.CTkFont(size=11), text_color="orange")
        token_label.pack(pady=2)

        # Prompt text
        prompt_frame = ctk.CTkFrame(main_frame)
        prompt_frame.pack(fill="both", expand=True, pady=(0, 10))

        prompt_title = ctk.CTkLabel(prompt_frame, text="� Custom Prompt",
                                   font=ctk.CTkFont(size=14, weight="bold"))
        prompt_title.pack(pady=(10, 5))

        prompt_text = ctk.CTkTextbox(prompt_frame, height=200, font=ctk.CTkFont(size=11))
        prompt_text.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        prompt_text.insert("1.0", base_prompt)
        prompt_text.configure(state="disabled")  # Make read-only

        # Buttons
        btn_frame = ctk.CTkFrame(main_frame)
        btn_frame.pack(fill="x")

        def copy_prompt():
            popup.clipboard_clear()
            popup.clipboard_append(base_prompt)
            self.util.add_status_frame(f"📋 Complete prompt copied (~{prompt_tokens:,} tokens)", "cyan", level=4)

        copy_btn = ctk.CTkButton(btn_frame, text="📋 Copy Prompt", command=copy_prompt, width=120)
        copy_btn.pack(side="left", padx=5, pady=10)

        close_btn = ctk.CTkButton(btn_frame, text="Close", command=popup.destroy, width=80)
        close_btn.pack(side="right", padx=5, pady=10)

    def show_analysis_confirmation_popup(self, bot_id, bot_name, chart_data_result, custom_prompt, ai_provider, symbol_full, timeframes, use_signal_format):
        """Show analysis confirmation popup with prompt preview and empty response area"""
        # Create popup dialog
        popup = ctk.CTkToplevel()
        popup.title(f"AI Analysis Confirmation - {bot_name}")
        popup.geometry("1200x700")  # Same size as analysis popup
        popup.transient(self.frame.winfo_toplevel())
        popup.grab_set()

        # Center the popup
        popup.update_idletasks()
        x = (popup.winfo_screenwidth() // 2) - (1200 // 2)
        y = (popup.winfo_screenheight() // 2) - (700 // 2)
        popup.geometry(f"1200x700+{x}+{y}")

        # Main content frame
        main_frame = ctk.CTkFrame(popup)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Header
        header_frame = ctk.CTkFrame(main_frame)
        header_frame.pack(fill="x", pady=(0, 10))

        title_label = ctk.CTkLabel(header_frame, text=f"🤖 AI Analysis Confirmation",
                                  font=ctk.CTkFont(size=18, weight="bold"))
        title_label.pack(pady=10)

        # Analysis info
        info_frame = ctk.CTkFrame(main_frame)
        info_frame.pack(fill="x", pady=(0, 10))

        timeframes_str = ', '.join(timeframes) if isinstance(timeframes, list) else str(timeframes)
        info_text = f"Symbol: {symbol_full} | Timeframes: {timeframes_str} | AI: {ai_provider.upper()}"
        info_label = ctk.CTkLabel(info_frame, text=info_text, font=ctk.CTkFont(size=12))
        info_label.pack(pady=5)

        # Generate the complete prompt for preview
        base_prompt = self.util.generate_prompt(chart_data_result, custom_prompt, symbol_full, timeframes, use_signal_format)

        # Token count info
        prompt_tokens = self.estimate_tokens(base_prompt)
        estimated_response_tokens = min(prompt_tokens // 2, 1000)
        total_estimated = prompt_tokens + estimated_response_tokens

        token_info_text = f"📊 Estimated Tokens: Prompt ~{prompt_tokens:,} | Response ~{estimated_response_tokens:,} | Total ~{total_estimated:,}"
        token_label = ctk.CTkLabel(info_frame, text=token_info_text,
                                  font=ctk.CTkFont(size=11), text_color="orange")
        token_label.pack(pady=2)

        # Two-column content frame
        content_frame = ctk.CTkFrame(main_frame)
        content_frame.pack(fill="both", expand=True, pady=(0, 10))

        # Left column - Complete Prompt Preview
        left_frame = ctk.CTkFrame(content_frame)
        left_frame.pack(side="left", fill="both", expand=True, padx=(0, 5))

        prompt_title = ctk.CTkLabel(left_frame, text="📝 Complete AI Prompt Preview",
                                   font=ctk.CTkFont(size=14, weight="bold"))
        prompt_title.pack(pady=(10, 5))

        prompt_text = ctk.CTkTextbox(left_frame, font=ctk.CTkFont(size=10))
        prompt_text.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        prompt_text.insert("1.0", base_prompt)
        prompt_text.configure(state="disabled")  # Make read-only

        # Right column - Response Area (Initially Empty)
        right_frame = ctk.CTkFrame(content_frame)
        right_frame.pack(side="right", fill="both", expand=True, padx=(5, 0))

        response_title = ctk.CTkLabel(right_frame, text="📊 AI Analysis Response",
                                     font=ctk.CTkFont(size=14, weight="bold"))
        response_title.pack(pady=(10, 5))

        # Store reference to response textbox for later updates
        self.response_text = ctk.CTkTextbox(right_frame, font=ctk.CTkFont(size=11))
        self.response_text.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        self.response_text.insert("1.0", "Click 'Analyze Now' to start AI analysis...")
        self.response_text.configure(state="disabled")

        # Store popup reference for later updates
        self.analysis_popup = popup

        # Buttons
        btn_frame = ctk.CTkFrame(main_frame)
        btn_frame.pack(fill="x")

        def start_analysis():
            """Start the actual AI analysis"""
            self.perform_actual_analysis(bot_id, bot_name, chart_data_result, custom_prompt, ai_provider, symbol_full, timeframes, use_signal_format, popup)

        # Close button on the left
        close_btn = ctk.CTkButton(btn_frame, text="Cancel", command=popup.destroy, width=80)
        close_btn.pack(side="left", padx=5, pady=10)

        # Analyze Now button on the right
        analyze_btn = ctk.CTkButton(btn_frame, text="🤖 Analyze Now", command=start_analysis,
                                   width=120, fg_color="purple", hover_color="darkviolet")
        analyze_btn.pack(side="right", padx=5, pady=10)

    def perform_actual_analysis(self, bot_id, bot_name, chart_data_result, custom_prompt, ai_provider, symbol_full, timeframes, use_signal_format, popup):
        """Perform the actual AI analysis with loading states"""
        try:
            # Show loading state
            self.response_text.configure(state="normal")
            self.response_text.delete("1.0", "end")
            self.response_text.insert("1.0", "🔄 Analyzing market data with AI...\n\nPlease wait while we process your request...")
            self.response_text.configure(state="disabled")

            # Update the popup to show loading
            popup.update()

            # Add status message
            self.util.add_status_frame(f"🤖 Starting AI analysis: {bot_name} ({symbol_full})", "cyan", level=3)

            # Perform AI analysis
            analysis_result = self.util.perform_ai_analysis(
                chart_data_result,
                custom_prompt,
                ai_provider,
                "",  # No image URL for now
                symbol_full,
                timeframes,
                use_signal_format
            )

            if analysis_result.get("error"):
                # Show error in response box
                error_msg = analysis_result.get("message", "AI analysis failed")
                self.response_text.configure(state="normal")
                self.response_text.delete("1.0", "end")
                self.response_text.insert("1.0", f"❌ Analysis Failed\n\n{error_msg}")
                self.response_text.configure(state="disabled")
                self.util.add_status_frame(f"❌ AI analysis failed for {bot_name}: {error_msg}", "red", level=2)
                return

            # Show successful result
            analysis_content = analysis_result.get("analysis", "No analysis available")
            self.response_text.configure(state="normal")
            self.response_text.delete("1.0", "end")
            self.response_text.insert("1.0", analysis_content)
            self.response_text.configure(state="disabled")

            # Update token info with actual usage
            complete_prompt = analysis_result.get("prompt", "")
            prompt_tokens = self.estimate_tokens(complete_prompt)
            actual_response_tokens = self.estimate_tokens(analysis_content)
            total_tokens = prompt_tokens + actual_response_tokens

            # Update the popup with actual token usage and add action buttons
            self.add_analysis_action_buttons(popup, analysis_result, bot_name, prompt_tokens, actual_response_tokens, total_tokens)

            # Save to logs
            log_path = self.util.save_signal_log(analysis_result, bot_name.replace(" ", "_"))

            # Update bot last used timestamp
            self.config.ai_bots[bot_id]["last_used"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.config.save_ai_bots()

            self.util.add_status_frame(f"✅ AI analysis completed: {bot_name}", "green", level=3)

        except Exception as e:
            # Show error in response box
            self.response_text.configure(state="normal")
            self.response_text.delete("1.0", "end")
            self.response_text.insert("1.0", f"❌ Analysis Error\n\n{str(e)}")
            self.response_text.configure(state="disabled")
            self.util.add_status_frame(f"❌ Analysis error for {bot_name}: {str(e)}", "red", level=1)

    def add_analysis_action_buttons(self, popup, analysis_result, bot_name, prompt_tokens, actual_response_tokens, total_tokens):
        """Add action buttons after successful analysis"""
        # Find the button frame (last child of main_frame)
        main_frame = popup.winfo_children()[0]  # Main content frame
        btn_frame = main_frame.winfo_children()[-1]  # Last child should be button frame

        # Clear existing buttons
        for widget in btn_frame.winfo_children():
            widget.destroy()

        # Update token display
        token_info_text = f"📊 Tokens Used: Prompt ~{prompt_tokens:,} | Response ~{actual_response_tokens:,} | Total ~{total_tokens:,}"

        # Find info frame and update token label
        info_frame = main_frame.winfo_children()[1]  # Second child should be info frame
        token_label = info_frame.winfo_children()[-1]  # Last child should be token label
        token_label.configure(text=token_info_text, text_color="lightgreen")

        def copy_analysis():
            popup.clipboard_clear()
            popup.clipboard_append(analysis_result.get("analysis", ""))
            self.util.add_status_frame(f"📋 Analysis copied (~{actual_response_tokens:,} tokens)", "cyan", level=4)

        def copy_prompt():
            popup.clipboard_clear()
            popup.clipboard_append(analysis_result.get("prompt", ""))
            self.util.add_status_frame(f"📋 Complete prompt copied (~{prompt_tokens:,} tokens)", "cyan", level=4)

        # Close button on the left
        close_btn = ctk.CTkButton(btn_frame, text="Close", command=popup.destroy, width=80)
        close_btn.pack(side="left", padx=5, pady=10)

        # Copy buttons on the right
        copy_prompt_btn = ctk.CTkButton(btn_frame, text="📋 Copy Prompt", command=copy_prompt, width=120)
        copy_prompt_btn.pack(side="right", padx=5, pady=10)

        copy_analysis_btn = ctk.CTkButton(btn_frame, text="📋 Copy Analysis", command=copy_analysis, width=120)
        copy_analysis_btn.pack(side="right", padx=5, pady=10)

        # Order placement button for structured signals
        if analysis_result.get("structured_signal") and analysis_result.get("signal_data"):
            def place_order():
                self.place_order_from_analysis(analysis_result, popup)

            order_btn = ctk.CTkButton(btn_frame, text="📈 Place Order", command=place_order,
                                     width=120, fg_color="green", hover_color="darkgreen")
            order_btn.pack(side="right", padx=5, pady=10)

    def show_analysis_popup(self, analysis_result, bot_name):
        """Show AI analysis results in a popup window with 2 columns"""
        # Create popup dialog
        popup = ctk.CTkToplevel()
        popup.title(f"AI Analysis Results - {bot_name}")
        popup.geometry("1200x700")  # Wider for 2 columns
        popup.transient(self.frame.winfo_toplevel())
        popup.grab_set()

        # Center the popup
        popup.update_idletasks()
        x = (popup.winfo_screenwidth() // 2) - (1200 // 2)
        y = (popup.winfo_screenheight() // 2) - (700 // 2)
        popup.geometry(f"1200x700+{x}+{y}")

        # Main content frame
        main_frame = ctk.CTkFrame(popup)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Header
        header_frame = ctk.CTkFrame(main_frame)
        header_frame.pack(fill="x", pady=(0, 10))

        title_label = ctk.CTkLabel(header_frame, text=f"🤖 AI Analysis Results",
                                  font=ctk.CTkFont(size=18, weight="bold"))
        title_label.pack(pady=10)

        # Analysis info
        info_frame = ctk.CTkFrame(main_frame)
        info_frame.pack(fill="x", pady=(0, 10))

        symbol = analysis_result.get("symbol", "Unknown")
        timeframes = analysis_result.get("timeframes", [])
        ai_provider = analysis_result.get("ai_provider", "Unknown")
        timestamp = analysis_result.get("timestamp", "Unknown")

        info_text = f"Symbol: {symbol} | Timeframes: {', '.join(timeframes)} | AI: {ai_provider} | Time: {timestamp[:19]}"
        info_label = ctk.CTkLabel(info_frame, text=info_text, font=ctk.CTkFont(size=12))
        info_label.pack(pady=5)

        # Token count info for analysis
        complete_prompt = analysis_result.get("prompt", "")
        analysis_text_content = analysis_result.get("analysis", "")

        prompt_tokens = self.estimate_tokens(complete_prompt)
        actual_response_tokens = self.estimate_tokens(analysis_text_content)
        total_tokens = prompt_tokens + actual_response_tokens

        token_info_text = f"📊 Tokens Used: Prompt ~{prompt_tokens:,} | Response ~{actual_response_tokens:,} | Total ~{total_tokens:,}"
        token_label = ctk.CTkLabel(info_frame, text=token_info_text,
                                  font=ctk.CTkFont(size=11), text_color="lightgreen")
        token_label.pack(pady=2)

        # Two-column content frame
        content_frame = ctk.CTkFrame(main_frame)
        content_frame.pack(fill="both", expand=True, pady=(0, 10))

        # Left column - Complete Prompt
        left_frame = ctk.CTkFrame(content_frame)
        left_frame.pack(side="left", fill="both", expand=True, padx=(0, 5))

        prompt_title = ctk.CTkLabel(left_frame, text="📝 Complete AI Prompt",
                                   font=ctk.CTkFont(size=14, weight="bold"))
        prompt_title.pack(pady=(10, 5))

        prompt_text = ctk.CTkTextbox(left_frame, font=ctk.CTkFont(size=10))
        prompt_text.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        complete_prompt = analysis_result.get("prompt", "No prompt available")
        prompt_text.insert("1.0", complete_prompt)
        prompt_text.configure(state="disabled")  # Make read-only

        # Right column - Analysis Result
        right_frame = ctk.CTkFrame(content_frame)
        right_frame.pack(side="right", fill="both", expand=True, padx=(5, 0))

        analysis_title = ctk.CTkLabel(right_frame, text="📊 AI Analysis Result",
                                     font=ctk.CTkFont(size=14, weight="bold"))
        analysis_title.pack(pady=(10, 5))

        analysis_text = ctk.CTkTextbox(right_frame, font=ctk.CTkFont(size=11))
        analysis_text.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        analysis_text.insert("1.0", analysis_result.get("analysis", "No analysis available"))
        analysis_text.configure(state="disabled")  # Make read-only

        # Buttons - rearranged as requested
        btn_frame = ctk.CTkFrame(main_frame)
        btn_frame.pack(fill="x")

        def copy_analysis():
            popup.clipboard_clear()
            popup.clipboard_append(analysis_result.get("analysis", ""))
            self.util.add_status_frame(f"📋 Analysis copied (~{actual_response_tokens:,} tokens)", "cyan", level=4)

        def copy_prompt_analysis():
            popup.clipboard_clear()
            popup.clipboard_append(complete_prompt)
            self.util.add_status_frame(f"📋 Complete prompt copied (~{prompt_tokens:,} tokens)", "cyan", level=4)

        # Close button on the left
        close_btn = ctk.CTkButton(btn_frame, text="Close", command=popup.destroy, width=80)
        close_btn.pack(side="left", padx=5, pady=10)

        # Copy buttons on the right
        copy_prompt_btn = ctk.CTkButton(btn_frame, text="📋 Copy Prompt", command=copy_prompt_analysis, width=120)
        copy_prompt_btn.pack(side="right", padx=5, pady=10)

        copy_analysis_btn = ctk.CTkButton(btn_frame, text="📋 Copy Analysis", command=copy_analysis, width=120)
        copy_analysis_btn.pack(side="right", padx=5, pady=10)

        # Order placement button for structured signals
        if analysis_result.get("structured_signal") and analysis_result.get("signal_data"):
            def place_order():
                self.place_order_from_analysis(analysis_result, popup)

            order_btn = ctk.CTkButton(btn_frame, text="📈 Place Order", command=place_order,
                                     width=120, fg_color="green", hover_color="darkgreen")
            order_btn.pack(side="right", padx=5, pady=10)

    def place_order_from_analysis(self, analysis_result, popup_window):
        """Extract signal data and place order using Input tab functionality"""
        try:
            signal_data = analysis_result.get("signal_data", {})
            if not signal_data:
                self.util.add_status_frame("❌ No signal data found for order placement", "red", level=2)
                return

            # Extract order parameters
            symbol = signal_data.get("symbol", "").replace("XAUUSD", "XU").replace("EURUSD", "EU")  # Convert to app format
            signal_type = signal_data.get("signal_type", "")
            entry_price = signal_data.get("entry_price", "")
            sl_price = signal_data.get("sl_price", "")

            # Extract TP prices
            tp_prices = []
            for i in range(1, 6):  # TP1 to TP5
                tp_key = f"tp{i}_price"
                tp_value = signal_data.get(tp_key, "")
                if tp_value and tp_value != "N/A":
                    try:
                        float(tp_value)  # Validate it's a number
                        tp_prices.append(tp_value)
                    except ValueError:
                        continue

            # Validate required fields
            if not all([symbol, signal_type, entry_price, sl_price]):
                self.util.add_status_frame("❌ Missing required order parameters", "red", level=2)
                return

            # Determine order type
            if "buy" in signal_type.lower():
                order_type = "Buy Limit"
            elif "sell" in signal_type.lower():
                order_type = "Sell Limit"
            else:
                self.util.add_status_frame("❌ Invalid signal type. Must be Buy Limit or Sell Limit", "red", level=2)
                return

            # Validate prices are numbers
            try:
                float(entry_price)
                float(sl_price)
                if not tp_prices:
                    self.util.add_status_frame("❌ No valid take profit prices found", "red", level=2)
                    return
            except ValueError:
                self.util.add_status_frame("❌ Invalid price format", "red", level=2)
                return

            # Use the same order placement logic as Input tab
            # Get the Input tab instance through the main app
            input_tab = None
            if hasattr(self.util, 'app') and hasattr(self.util.app, 'tabs'):
                for _, tab_instance in self.util.app.tabs.items():
                    if hasattr(tab_instance, 'place_order') and hasattr(tab_instance, 'symbol_var'):
                        input_tab = tab_instance
                        break

            if not input_tab:
                self.util.add_status_frame("❌ Input tab not found for order placement. Please place order manually.", "orange", level=2)
                return

            # Set the parameters in Input tab
            input_tab.symbol_var.set(symbol)
            input_tab.order_type_var.set(order_type)
            input_tab.price_entry.delete(0, "end")
            input_tab.price_entry.insert(0, str(entry_price))
            input_tab.sl_entry.delete(0, "end")
            input_tab.sl_entry.insert(0, str(sl_price))

            # Set TP prices (up to 5)
            tp_entries = [input_tab.tp1_entry, input_tab.tp2_entry, input_tab.tp3_entry,
                         input_tab.tp4_entry, input_tab.tp5_entry]

            # Clear all TP entries first
            for tp_entry in tp_entries:
                tp_entry.delete(0, "end")

            # Set available TP prices
            for i, tp_price in enumerate(tp_prices[:5]):  # Max 5 TPs
                if i < len(tp_entries):
                    tp_entries[i].insert(0, str(tp_price))

            # Place the order
            input_tab.place_order()

            # Show success message
            reason = signal_data.get("reason", "AI analysis signal")
            self.util.add_status_frame(f"📈 Order placed from AI signal: {symbol} {order_type} @ {entry_price}", "green", level=3)
            self.util.add_status_frame(f"💡 Reason: {reason}", "cyan", level=4)

            # Close the popup
            popup_window.destroy()

        except Exception as e:
            self.util.add_status_frame(f"❌ Order placement failed: {str(e)}", "red", level=1)

    def edit_bot(self, bot_id):
        """Edit an existing AI bot (opens edit dialog)"""
        if bot_id not in self.config.ai_bots:
            self.util.add_status_frame(f"❌ Bot {bot_id} not found", "red")
            return

        # Create edit dialog
        self.create_edit_dialog(bot_id)

    def delete_bot(self, bot_id):
        """Delete an AI bot with confirmation"""
        if bot_id not in self.config.ai_bots:
            self.util.add_status_frame(f"❌ Bot {bot_id} not found", "red")
            return

        bot_name = self.config.ai_bots[bot_id].get("name", f"Bot {bot_id}")

        # Create confirmation dialog
        dialog = ctk.CTkToplevel()
        dialog.title("Confirm Delete")
        dialog.geometry("300x150")
        dialog.transient(self.frame.winfo_toplevel())
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (300 // 2)
        y = (dialog.winfo_screenheight() // 2) - (150 // 2)
        dialog.geometry(f"300x150+{x}+{y}")

        # Dialog content
        ctk.CTkLabel(dialog, text=f"Delete AI Bot?",
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)
        ctk.CTkLabel(dialog, text=f"'{bot_name}'",
                    font=ctk.CTkFont(size=12)).pack(pady=5)
        ctk.CTkLabel(dialog, text="This action cannot be undone.",
                    font=ctk.CTkFont(size=10), text_color="red").pack(pady=5)

        # Buttons
        btn_frame = ctk.CTkFrame(dialog)
        btn_frame.pack(pady=10)

        def confirm_delete():
            # Delete using JSON storage
            self.config.delete_ai_bot(bot_id)
            self.util.add_status_frame(f"🗑️ Deleted AI bot: {bot_name}", "orange")
            self.refresh_bot_list()
            dialog.destroy()

        def cancel_delete():
            dialog.destroy()

        ctk.CTkButton(btn_frame, text="Delete", fg_color="red", hover_color="darkred",
                     command=confirm_delete, width=80).pack(side="left", padx=5)
        ctk.CTkButton(btn_frame, text="Cancel", command=cancel_delete, width=80).pack(side="left", padx=5)

    def test_all_bots(self):
        """Test all enabled AI bots"""
        enabled_bots = {bot_id: config for bot_id, config in self.config.ai_bots.items()
                       if config.get("enabled", True)}

        if not enabled_bots:
            self.util.add_status_frame("❌ No enabled AI bots to test", "orange")
            return

        self.util.add_status_frame(f"🧪 Testing {len(enabled_bots)} enabled AI bots...", "cyan")

        for bot_id in enabled_bots:
            self.test_bot(bot_id)

    def create_edit_dialog(self, bot_id):
        """Create edit dialog for modifying bot configuration"""
        bot_config = self.config.ai_bots[bot_id]

        # Create edit dialog
        dialog = ctk.CTkToplevel()
        dialog.title(f"Edit AI Bot: {bot_config.get('name', bot_id)}")
        dialog.geometry("500x400")
        dialog.transient(self.frame.winfo_toplevel())
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (dialog.winfo_screenheight() // 2) - (400 // 2)
        dialog.geometry(f"500x400+{x}+{y}")

        # Dialog content
        content_frame = ctk.CTkScrollableFrame(dialog)
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Form fields
        ctk.CTkLabel(content_frame, text="Bot Name:").pack(anchor="w", pady=(0, 5))
        edit_name = ctk.CTkEntry(content_frame, width=400)
        edit_name.pack(fill="x", pady=(0, 10))
        edit_name.insert(0, bot_config.get("name", ""))

        ctk.CTkLabel(content_frame, text="Symbol:").pack(anchor="w", pady=(0, 5))
        edit_symbol = ctk.CTkOptionMenu(content_frame, values=list(self.config.symbols.keys()), width=400)
        edit_symbol.pack(fill="x", pady=(0, 10))
        edit_symbol.set(bot_config.get("symbol", "EU"))

        # Multi-timeframe selection
        ctk.CTkLabel(content_frame, text="Timeframes:").pack(anchor="w", pady=(0, 5))
        timeframe_frame = ctk.CTkFrame(content_frame)
        timeframe_frame.pack(fill="x", pady=(0, 10))

        # Create checkboxes for each timeframe
        edit_timeframe_vars = {}
        edit_timeframe_checkboxes = {}

        available_timeframes = list(self.config.timeframes.keys())
        for i, tf in enumerate(available_timeframes):
            var = ctk.BooleanVar()
            checkbox = ctk.CTkCheckBox(timeframe_frame, text=tf, variable=var)

            # Arrange in rows of 4
            row = i // 4
            col = i % 4
            checkbox.grid(row=row, column=col, sticky="w", padx=5, pady=2)

            edit_timeframe_vars[tf] = var
            edit_timeframe_checkboxes[tf] = checkbox

        # Set current timeframes
        current_timeframes = bot_config.get("timeframes", [bot_config.get("timeframe", "H1")])
        if not isinstance(current_timeframes, list):
            current_timeframes = [current_timeframes]

        for tf in current_timeframes:
            if tf in edit_timeframe_vars:
                edit_timeframe_vars[tf].set(True)

        ctk.CTkLabel(content_frame, text="Bars Back:").pack(anchor="w", pady=(0, 5))
        edit_bars = ctk.CTkEntry(content_frame, width=400)
        edit_bars.pack(fill="x", pady=(0, 10))
        edit_bars.insert(0, str(bot_config.get("bars_back", 100)))

        ctk.CTkLabel(content_frame, text="Custom Prompt:").pack(anchor="w", pady=(0, 5))
        edit_prompt = ctk.CTkTextbox(content_frame, height=120, width=400)
        edit_prompt.pack(fill="x", pady=(0, 10))
        edit_prompt.insert("1.0", bot_config.get("prompt", ""))

        # AI API Provider
        ctk.CTkLabel(content_frame, text="AI Provider:").pack(anchor="w", pady=(0, 5))
        edit_api = ctk.CTkOptionMenu(content_frame, values=["gpt", "gemini"], width=400)
        edit_api.pack(fill="x", pady=(0, 10))
        edit_api.set(bot_config.get("api_provider", "gpt"))

        # Auto Place Order Toggle
        ctk.CTkLabel(content_frame, text="Auto Place Order:").pack(anchor="w", pady=(0, 5))
        edit_auto_order = ctk.CTkSwitch(content_frame, text="Enable automatic order placement")
        edit_auto_order.pack(anchor="w", pady=(0, 10))
        if bot_config.get("auto_place_order", False):
            edit_auto_order.select()

        # Schedule Check Toggle
        ctk.CTkLabel(content_frame, text="Schedule Check:").pack(anchor="w", pady=(0, 5))
        schedule_edit_frame = ctk.CTkFrame(content_frame)
        schedule_edit_frame.pack(fill="x", pady=(0, 10))

        edit_schedule = ctk.CTkSwitch(schedule_edit_frame, text="Enable scheduled checks")
        edit_schedule.pack(anchor="w", pady=(0, 5))
        if bot_config.get("schedule_check", False):
            edit_schedule.select()

        # Schedule type selection
        schedule_type_frame = ctk.CTkFrame(schedule_edit_frame)
        schedule_type_frame.pack(fill="x", pady=(5, 0))

        ctk.CTkLabel(schedule_type_frame, text="Type:").pack(side="left", padx=(0, 5))
        edit_schedule_type = ctk.CTkOptionMenu(
            schedule_type_frame,
            values=["custom", "hourly", "daily", "weekly", "monthly"],
            width=120
        )
        edit_schedule_type.pack(side="left", padx=(0, 10))
        edit_schedule_type.set(bot_config.get("schedule_type", "custom"))

        # Dynamic schedule input frame
        schedule_input_frame = ctk.CTkFrame(schedule_type_frame)
        schedule_input_frame.pack(side="left", fill="x", expand=True)

        # Function to build schedule inputs based on type
        def build_schedule_inputs(selected_type=None):
            # Clear existing widgets
            for widget in schedule_input_frame.winfo_children():
                widget.destroy()

            current_type = selected_type or edit_schedule_type.get()

            if current_type == "custom":
                ctk.CTkLabel(schedule_input_frame, text="Minutes:").pack(side="left", padx=(10, 5))
                edit_custom_minutes = ctk.CTkEntry(schedule_input_frame, width=80)
                edit_custom_minutes.pack(side="left")
                edit_custom_minutes.insert(0, str(bot_config.get("schedule_custom_minutes", 60)))
                # Store reference for saving
                schedule_input_frame.custom_minutes_entry = edit_custom_minutes

            elif current_type == "hourly":
                ctk.CTkLabel(schedule_input_frame, text="Every hour").pack(side="left", padx=(10, 0))

            elif current_type == "daily":
                ctk.CTkLabel(schedule_input_frame, text="Time:").pack(side="left", padx=(10, 5))
                edit_daily_time = ctk.CTkEntry(schedule_input_frame, width=80, placeholder_text="HH:MM")
                edit_daily_time.pack(side="left")
                edit_daily_time.insert(0, bot_config.get("schedule_daily_time", "09:00"))
                # Store reference for saving
                schedule_input_frame.daily_time_entry = edit_daily_time

            elif current_type == "weekly":
                ctk.CTkLabel(schedule_input_frame, text="Day:").pack(side="left", padx=(10, 5))
                edit_weekly_day = ctk.CTkOptionMenu(
                    schedule_input_frame,
                    values=["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
                    width=100
                )
                edit_weekly_day.pack(side="left", padx=(0, 5))
                edit_weekly_day.set(bot_config.get("schedule_weekly_day", "Monday"))

                ctk.CTkLabel(schedule_input_frame, text="Time:").pack(side="left", padx=(5, 5))
                edit_weekly_time = ctk.CTkEntry(schedule_input_frame, width=80, placeholder_text="HH:MM")
                edit_weekly_time.pack(side="left")
                edit_weekly_time.insert(0, bot_config.get("schedule_weekly_time", "09:00"))
                # Store references for saving
                schedule_input_frame.weekly_day_menu = edit_weekly_day
                schedule_input_frame.weekly_time_entry = edit_weekly_time

            elif current_type == "monthly":
                ctk.CTkLabel(schedule_input_frame, text="Day:").pack(side="left", padx=(10, 5))
                edit_monthly_day = ctk.CTkEntry(schedule_input_frame, width=50, placeholder_text="1-31")
                edit_monthly_day.pack(side="left", padx=(0, 5))
                edit_monthly_day.insert(0, str(bot_config.get("schedule_monthly_day", 1)))

                ctk.CTkLabel(schedule_input_frame, text="Time:").pack(side="left", padx=(5, 5))
                edit_monthly_time = ctk.CTkEntry(schedule_input_frame, width=80, placeholder_text="HH:MM")
                edit_monthly_time.pack(side="left")
                edit_monthly_time.insert(0, bot_config.get("schedule_monthly_time", "09:00"))
                # Store references for saving
                schedule_input_frame.monthly_day_entry = edit_monthly_day
                schedule_input_frame.monthly_time_entry = edit_monthly_time

        # Build initial schedule inputs
        build_schedule_inputs()

        # Set callback for schedule type change
        edit_schedule_type.configure(command=build_schedule_inputs)

        # Chart Image
        ctk.CTkLabel(content_frame, text="Chart Image:").pack(anchor="w", pady=(0, 5))
        image_edit_frame = ctk.CTkFrame(content_frame)
        image_edit_frame.pack(fill="x", pady=(0, 10))

        edit_use_image = ctk.CTkSwitch(image_edit_frame, text="Use chart image")
        edit_use_image.pack(side="left")
        if bot_config.get("use_chart_image", False):
            edit_use_image.select()

        edit_image_path = bot_config.get("chart_image_path", "")
        edit_image_btn = ctk.CTkButton(image_edit_frame, text="📁 Select Image", width=120)
        edit_image_btn.pack(side="left", padx=(20, 0))

        if edit_image_path:
            file_name = os.path.basename(edit_image_path)
            if len(file_name) > 15:
                file_name = file_name[:12] + "..."
            edit_image_btn.configure(text=f"📁 {file_name}")

        def select_edit_image():
            file_types = [
                ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"),
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("All files", "*.*")
            ]

            filename = filedialog.askopenfilename(
                title="Select Chart Image",
                filetypes=file_types
            )

            if filename:
                nonlocal edit_image_path
                edit_image_path = filename
                file_name = os.path.basename(filename)
                if len(file_name) > 15:
                    file_name = file_name[:12] + "..."
                edit_image_btn.configure(text=f"📁 {file_name}")

        edit_image_btn.configure(command=select_edit_image)

        # Signal Format Toggle
        ctk.CTkLabel(content_frame, text="Signal Format:").pack(anchor="w", pady=(10, 5))
        edit_signal_format = ctk.CTkSwitch(content_frame, text="Use Structured Signal Format")
        edit_signal_format.pack(anchor="w", pady=(0, 10))
        if bot_config.get("use_signal_format", False):
            edit_signal_format.select()

        # Buttons
        btn_frame = ctk.CTkFrame(content_frame)
        btn_frame.pack(fill="x", pady=20)

        def save_changes():
            try:
                # Validate inputs
                name = edit_name.get().strip()
                if not name:
                    self.util.add_status_frame("❌ Bot name is required", "red")
                    return

                try:
                    bars_back = int(edit_bars.get())
                    if bars_back <= 0:
                        raise ValueError("Bars back must be positive")
                except ValueError:
                    self.util.add_status_frame("❌ Invalid bars back value", "red")
                    return

                prompt = edit_prompt.get("1.0", "end-1c").strip()
                if not prompt:
                    self.util.add_status_frame("❌ Custom prompt is required", "red", level=2)
                    return

                # Get additional configuration
                api_provider = edit_api.get()
                auto_place_order = edit_auto_order.get()
                schedule_check = edit_schedule.get()

                # Get schedule settings
                schedule_type = edit_schedule_type.get()
                schedule_custom_minutes = 60
                schedule_daily_time = "09:00"
                schedule_weekly_day = "Monday"
                schedule_weekly_time = "09:00"
                schedule_monthly_day = 1
                schedule_monthly_time = "09:00"

                # Extract schedule values based on type
                try:
                    if schedule_type == "custom" and hasattr(schedule_input_frame, 'custom_minutes_entry'):
                        schedule_custom_minutes = int(schedule_input_frame.custom_minutes_entry.get())
                        if schedule_custom_minutes <= 0:
                            raise ValueError("Custom minutes must be positive")

                    elif schedule_type == "daily" and hasattr(schedule_input_frame, 'daily_time_entry'):
                        schedule_daily_time = schedule_input_frame.daily_time_entry.get()
                        # Validate time format
                        from datetime import datetime
                        datetime.strptime(schedule_daily_time, "%H:%M")

                    elif schedule_type == "weekly":
                        if hasattr(schedule_input_frame, 'weekly_day_menu'):
                            schedule_weekly_day = schedule_input_frame.weekly_day_menu.get()
                        if hasattr(schedule_input_frame, 'weekly_time_entry'):
                            schedule_weekly_time = schedule_input_frame.weekly_time_entry.get()
                            # Validate time format
                            from datetime import datetime
                            datetime.strptime(schedule_weekly_time, "%H:%M")

                    elif schedule_type == "monthly":
                        if hasattr(schedule_input_frame, 'monthly_day_entry'):
                            schedule_monthly_day = int(schedule_input_frame.monthly_day_entry.get())
                            if not (1 <= schedule_monthly_day <= 31):
                                raise ValueError("Monthly day must be between 1 and 31")
                        if hasattr(schedule_input_frame, 'monthly_time_entry'):
                            schedule_monthly_time = schedule_input_frame.monthly_time_entry.get()
                            # Validate time format
                            from datetime import datetime
                            datetime.strptime(schedule_monthly_time, "%H:%M")

                except ValueError as e:
                    self.util.add_status_frame(f"❌ Invalid schedule settings: {e}", "red", level=2)
                    return

                use_chart_image = edit_use_image.get()
                chart_image_path = edit_image_path if use_chart_image else ""
                use_signal_format = edit_signal_format.get()

                # Get selected timeframes
                selected_timeframes = []
                for tf, var in edit_timeframe_vars.items():
                    if var.get():
                        selected_timeframes.append(tf)

                if not selected_timeframes:
                    self.util.add_status_frame("❌ At least one timeframe must be selected", "red", level=2)
                    return

                # Update bot configuration
                updated_config = {
                    "name": name,
                    "symbol": edit_symbol.get(),
                    "timeframes": selected_timeframes,  # Multi-timeframe support
                    "timeframe": selected_timeframes[0] if selected_timeframes else "H1",  # Legacy support
                    "bars_back": bars_back,
                    "prompt": prompt,
                    "api_provider": api_provider,
                    "auto_place_order": auto_place_order,
                    "schedule_check": schedule_check,
                    "schedule_type": schedule_type,
                    "schedule_custom_minutes": schedule_custom_minutes,
                    "schedule_daily_time": schedule_daily_time,
                    "schedule_weekly_day": schedule_weekly_day,
                    "schedule_weekly_time": schedule_weekly_time,
                    "schedule_monthly_day": schedule_monthly_day,
                    "schedule_monthly_time": schedule_monthly_time,
                    "chart_image_path": chart_image_path,
                    "use_chart_image": use_chart_image,
                    "use_signal_format": use_signal_format,
                    "multi_timeframe": len(selected_timeframes) > 1,
                    # Preserve existing fields
                    "created_at": self.config.ai_bots[bot_id].get("created_at", ""),
                    "last_used": self.config.ai_bots[bot_id].get("last_used", None),
                    "enabled": self.config.ai_bots[bot_id].get("enabled", True)
                }

                # Update using JSON storage
                self.config.update_ai_bot(bot_id, updated_config)

                self.util.add_status_frame(f"✅ Updated AI bot: {name}", "green")
                self.refresh_bot_list()
                dialog.destroy()

            except Exception as e:
                self.util.add_status_frame(f"❌ Error updating bot: {e}", "red")

        def cancel_edit():
            dialog.destroy()

        ctk.CTkButton(btn_frame, text="💾 Save Changes", command=save_changes, width=120).pack(side="left", padx=5)
        ctk.CTkButton(btn_frame, text="Cancel", command=cancel_edit, width=80).pack(side="left", padx=5)

    # AI Bot Scheduler Methods
    def start_scheduler(self):
        """Start the AI bot scheduler thread"""
        if not self.scheduler_running:
            self.scheduler_running = True
            self.scheduler_thread = threading.Thread(target=self.scheduler_loop, daemon=True)
            self.scheduler_thread.start()
            self.util.add_status_frame("🤖 AI Bot scheduler started", "cyan", level=4)

    def stop_scheduler(self):
        """Stop the AI bot scheduler thread"""
        self.scheduler_running = False
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=2.0)
        self.util.add_status_frame("🤖 AI Bot scheduler stopped", "yellow", level=4)

    def scheduler_loop(self):
        """Main scheduler loop that checks all bots for scheduled analysis"""
        while self.scheduler_running:
            try:
                current_time = datetime.now()

                # Check each bot for scheduled analysis
                for bot_id, bot_config in self.config.ai_bots.items():
                    if not bot_config.get("enabled", True):
                        continue

                    if not bot_config.get("schedule_check", False):
                        continue

                    # Check if it's time to run this bot
                    if self.should_run_bot_analysis(bot_id, bot_config, current_time):
                        self.run_scheduled_bot_analysis(bot_id, bot_config)

                # Sleep for 1 minute before next check
                time.sleep(60)

            except Exception as e:
                self.util.add_status_frame(f"❌ Scheduler error: {e}", "red", level=2)
                time.sleep(60)

    def should_run_bot_analysis(self, bot_id, bot_config, current_time):
        """Determine if a bot should run analysis based on its schedule"""
        try:
            schedule_type = bot_config.get("schedule_type", "custom")

            # Get last run time for this bot
            last_run = self.bot_schedules.get(bot_id)

            if not last_run:
                # First run
                return True

            if schedule_type == "custom":
                check_interval = bot_config.get("schedule_custom_minutes", 60)
                time_diff = (current_time - last_run).total_seconds()
                required_interval = check_interval * 60  # Convert minutes to seconds
                return time_diff >= required_interval

            elif schedule_type == "hourly":
                time_diff = (current_time - last_run).total_seconds()
                return time_diff >= 3600  # 1 hour

            elif schedule_type == "daily":
                target_time_str = bot_config.get("schedule_daily_time", "09:00")
                try:
                    target_time = datetime.strptime(target_time_str, "%H:%M").time()
                    today_target = datetime.combine(current_time.date(), target_time)

                    # If we've passed today's target time and haven't run today
                    if current_time >= today_target and last_run.date() < current_time.date():
                        return True
                except ValueError:
                    # Invalid time format, fall back to hourly
                    time_diff = (current_time - last_run).total_seconds()
                    return time_diff >= 3600

            elif schedule_type == "weekly":
                weekdays = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
                target_weekday_str = bot_config.get("schedule_weekly_day", "Monday")
                target_time_str = bot_config.get("schedule_weekly_time", "09:00")

                try:
                    target_weekday = weekdays.index(target_weekday_str)
                    target_time = datetime.strptime(target_time_str, "%H:%M").time()

                    # If today is the target weekday and we've passed the target time
                    if current_time.weekday() == target_weekday:
                        today_target = datetime.combine(current_time.date(), target_time)
                        if current_time >= today_target and last_run.date() < current_time.date():
                            return True
                except (ValueError, IndexError):
                    # Invalid settings, fall back to daily
                    time_diff = (current_time - last_run).total_seconds()
                    return time_diff >= 86400  # 24 hours

            elif schedule_type == "monthly":
                target_day = bot_config.get("schedule_monthly_day", 1)
                target_time_str = bot_config.get("schedule_monthly_time", "09:00")

                try:
                    target_time = datetime.strptime(target_time_str, "%H:%M").time()

                    # Check if today is the target day of the month
                    if current_time.day == target_day:
                        today_target = datetime.combine(current_time.date(), target_time)
                        if current_time >= today_target and last_run.date() < current_time.date():
                            return True
                except ValueError:
                    # Invalid time format, fall back to monthly check (30 days)
                    time_diff = (current_time - last_run).total_seconds()
                    return time_diff >= 2592000  # 30 days

            return False

        except Exception as e:
            self.util.add_status_frame(f"❌ Schedule check error for bot {bot_id}: {e}", "red", level=2)
            return False

    def run_scheduled_bot_analysis(self, bot_id, bot_config):
        """Run analysis for a scheduled bot"""
        try:
            bot_name = bot_config.get("name", f"Bot {bot_id}")
            self.util.add_status_frame(f"🕒 Running scheduled analysis for: {bot_name}", "cyan", level=3)

            # Update last run time
            self.bot_schedules[bot_id] = datetime.now()

            # Run the analysis in a separate thread to avoid blocking the scheduler
            analysis_thread = threading.Thread(
                target=self.run_bot_analysis_with_auto_order,
                args=(bot_id, bot_config),
                daemon=True
            )
            analysis_thread.start()

        except Exception as e:
            self.util.add_status_frame(f"❌ Scheduled analysis error for bot {bot_id}: {e}", "red", level=2)

    def run_bot_analysis_with_auto_order(self, bot_id, bot_config):
        """Run bot analysis and potentially place order automatically"""
        try:
            bot_name = bot_config.get("name", f"Bot {bot_id}")

            # Get analysis parameters
            symbol_key = bot_config.get("symbol")
            symbol_full = self.util.get_symbol(symbol_key)
            timeframes = bot_config.get("timeframes", [bot_config.get("timeframe", "H1")])
            if not isinstance(timeframes, list):
                timeframes = [timeframes]

            barback = bot_config.get("bars_back", 100)
            custom_prompt = bot_config.get("prompt", "")
            ai_provider = bot_config.get("api_provider", "gpt")
            use_signal_format = bot_config.get("use_signal_format", False)
            auto_place_order = bot_config.get("auto_place_order", False)

            self.util.add_status_frame(f"📊 Scheduled analysis: {bot_name} ({symbol_full} {timeframes})", "cyan", level=3)

            # Get chart data
            chart_data_result = self.util.get_multi_timeframe_data(symbol_full, timeframes, barback)
            if not chart_data_result:
                self.util.add_status_frame(f"❌ Failed to get chart data for scheduled {bot_name}", "red", level=2)
                return

            # Perform AI analysis
            analysis_result = self.util.perform_ai_analysis(
                chart_data_result, custom_prompt, ai_provider, use_signal_format
            )

            if not analysis_result or analysis_result.get("error"):
                error_msg = analysis_result.get("message", "Unknown error") if analysis_result else "No response"
                self.util.add_status_frame(f"❌ Scheduled AI analysis failed for {bot_name}: {error_msg}", "red", level=2)
                return

            # Update bot last used timestamp
            self.config.ai_bots[bot_id]["last_used"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.config.save_ai_bots()

            # Log successful analysis
            self.util.add_status_frame(f"✅ Scheduled analysis completed for {bot_name}", "green", level=3)

            # Check if auto place order is enabled
            if auto_place_order:
                signal_data = analysis_result.get("signal_data", {})
                if signal_data and signal_data.get("signal_type"):
                    self.util.add_status_frame(f"📈 Auto-placing order for {bot_name}...", "green", level=3)
                    self.place_order_from_scheduled_analysis(analysis_result, bot_name)
                else:
                    self.util.add_status_frame(f"ℹ️ No valid signal found for auto order: {bot_name}", "yellow", level=3)
            else:
                self.util.add_status_frame(f"ℹ️ Analysis completed for {bot_name} (auto order disabled)", "cyan", level=3)

        except Exception as e:
            self.util.add_status_frame(f"❌ Scheduled bot analysis error for {bot_id}: {e}", "red", level=1)

    def place_order_from_scheduled_analysis(self, analysis_result, bot_name):
        """Place order from scheduled analysis (no popup window)"""
        try:
            signal_data = analysis_result.get("signal_data", {})
            if not signal_data:
                self.util.add_status_frame(f"❌ No signal data found for auto order: {bot_name}", "red", level=2)
                return

            # Extract order parameters
            symbol = signal_data.get("symbol", "").replace("XAUUSD", "XU").replace("EURUSD", "EU")
            signal_type = signal_data.get("signal_type", "")
            entry_price = signal_data.get("entry_price", "")
            sl_price = signal_data.get("sl_price", "")

            # Extract TP prices
            tp_prices = []
            for i in range(1, 6):  # TP1 to TP5
                tp_key = f"tp{i}_price"
                tp_value = signal_data.get(tp_key, "")
                if tp_value and tp_value != "N/A":
                    try:
                        float(tp_value)  # Validate it's a number
                        tp_prices.append(tp_value)
                    except ValueError:
                        continue

            # Validate required fields
            if not all([symbol, signal_type, entry_price, sl_price]):
                self.util.add_status_frame(f"❌ Missing required order parameters for {bot_name}", "red", level=2)
                return

            # Determine order type
            if "buy" in signal_type.lower():
                order_type = "Buy Limit"
            elif "sell" in signal_type.lower():
                order_type = "Sell Limit"
            else:
                self.util.add_status_frame(f"❌ Invalid signal type for {bot_name}: {signal_type}", "red", level=2)
                return

            # Validate prices are numbers
            try:
                float(entry_price)
                float(sl_price)
                if not tp_prices:
                    self.util.add_status_frame(f"❌ No valid TP prices for {bot_name}", "red", level=2)
                    return
            except ValueError:
                self.util.add_status_frame(f"❌ Invalid price format for {bot_name}", "red", level=2)
                return

            # Get the Input tab instance for order placement
            input_tab = None
            if hasattr(self.config, 'orders_tab_ref') and hasattr(self.config.orders_tab_ref, 'util'):
                # Try to find input tab through the main app structure
                try:
                    # This is a bit hacky but necessary to access other tabs
                    main_app = self.util.config if hasattr(self.util, 'config') else None
                    if hasattr(main_app, 'input_tab_ref'):
                        input_tab = main_app.input_tab_ref
                except:
                    pass

            if not input_tab:
                self.util.add_status_frame(f"❌ Input tab not found for auto order: {bot_name}", "orange", level=2)
                return

            # Set the parameters in Input tab
            input_tab.symbol_var.set(symbol)
            input_tab.order_type_var.set(order_type)
            input_tab.price_entry.delete(0, "end")
            input_tab.price_entry.insert(0, str(entry_price))
            input_tab.sl_entry.delete(0, "end")
            input_tab.sl_entry.insert(0, str(sl_price))

            # Set TP prices (up to 5)
            tp_entries = [input_tab.tp1_entry, input_tab.tp2_entry, input_tab.tp3_entry,
                         input_tab.tp4_entry, input_tab.tp5_entry]

            # Clear all TP entries first
            for tp_entry in tp_entries:
                tp_entry.delete(0, "end")

            # Set available TP prices
            for i, tp_price in enumerate(tp_prices[:5]):  # Max 5 TPs
                if i < len(tp_entries):
                    tp_entries[i].insert(0, str(tp_price))

            # Place the order
            input_tab.place_order()

            # Show success message
            reason = signal_data.get("reason", "AI scheduled analysis")
            self.util.add_status_frame(f"📈 Auto order placed from {bot_name}: {symbol} {order_type} @ {entry_price}", "green", level=3)
            self.util.add_status_frame(f"💡 Reason: {reason}", "cyan", level=4)

        except Exception as e:
            self.util.add_status_frame(f"❌ Auto order placement failed for {bot_name}: {str(e)}", "red", level=1)

    def cleanup_scheduler(self):
        """Cleanup scheduler thread when application closes"""
        if self.scheduler_running:
            self.stop_scheduler()
