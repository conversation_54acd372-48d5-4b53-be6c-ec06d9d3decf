
import MetaTrader5 as mt5
import customtkinter as ctk
import os
import threading
import time
from datetime import datetime, timedelta
import tkinter as tk
from tkinter import ttk
import re
from collections import defaultdict
from App.debug_logger import DebugLogger
import json

# ===========================
# Class: TabOrders
# ===========================
class TabOrders:
    def __init__(self, master, config, util):
        self.frame = master.add("Orders")
        self.config = config
        self.util = util

        # Initialize debug logger
        self.debug_logger = DebugLogger(config, util)
        self.loop_running = False
        self.thread1 = None
        self.time_var = ctk.IntVar(value=5)  # Default 5 seconds refresh

        # Separate controls for auto refresh and auto BE
        self.auto_refresh_enabled = False  # Auto refresh disabled by default
        self.auto_be_enabled = True  # Auto BE can work independently

        # Auto SL to BE settings for each filter type (using config)
        self.auto_be_settings = {}
        self.auto_tp_settings = {}
        for group_key, group_config in self.config.order_groups.items():
            self.auto_be_settings[group_key] = group_config["default_sl_enabled"]
            self.auto_tp_settings[group_key] = group_config["default_tp_enabled"]

        # Circuit breaker for MT5 operations
        self.mt5_error_count = 0
        self.max_mt5_errors = 5
        self.last_successful_operation = time.time()

        # Schedule check functionality
        self.schedule_enabled = False
        self.schedule_type = "custom"  # custom, hourly, daily, weekly, monthly
        self.schedule_custom_minutes = 60  # For custom schedule
        self.schedule_daily_time = "09:00"  # For daily schedule
        self.schedule_weekly_day = "Monday"  # For weekly schedule
        self.schedule_weekly_time = "09:00"  # For weekly schedule
        self.schedule_monthly_day = 1  # For monthly schedule (1-31)
        self.schedule_monthly_time = "09:00"  # For monthly schedule
        self.last_schedule_check = None
        self.schedule_thread = None
        self.schedule_running = False

        # Market hours monitoring for auto-restart
        self.market_monitor_thread = None
        self.market_monitor_running = False
        self.last_market_check = None
        self.market_was_closed = False

        # Load schedule settings from file
        self.schedule_settings_file = "schedule_settings.json"
        self.load_schedule_settings()

        # Create main control frame
        self.control_frame = ctk.CTkFrame(self.frame)
        self.control_frame.pack(fill="x", padx=10, pady=5)

        # Create tabview for subtabs
        self.tabview = ctk.CTkTabview(self.frame)
        self.tabview.pack(fill="both", expand=True, padx=10, pady=5)

        # Create subtabs dynamically based on config
        self.group_tabs = {}
        self.group_widgets = {}
        for group_key, group_config in self.config.order_groups.items():
            if group_config.get("has_subtab", False):
                tab = self.tabview.add(group_config["subtab_name"])
                self.group_tabs[group_key] = tab
                self.group_widgets[group_key] = {}

        self.build_control_panel()
        self.build_dynamic_subtabs()

        # Initialize status display
        self.update_status_display()

        # Perform initial data load
        self.refresh_all_data()

        # Initialize schedule UI state
        if self.schedule_enabled:
            self.schedule_switch.select()
            self.start_schedule_check()

        # Update schedule status display
        self.update_schedule_status()

        # Start loop if auto BE is enabled (even if auto refresh is disabled)
        if self.has_auto_be_enabled():
            self.start_loop()
            self.util.add_status_frame("📋 TabOrders initialized - Auto BE enabled, auto refresh disabled")
        else:
            self.util.add_status_frame("📋 TabOrders initialized - All auto functions disabled")

    def build_control_panel(self):
        """Build the control panel with refresh settings"""
        # Create a more organized layout with frames using grid for better space management

        # Top row - Refresh controls
        top_frame = ctk.CTkFrame(self.control_frame)
        top_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(top_frame, text="Auto Refresh:", font=ctk.CTkFont(weight="bold")).pack(side="left", padx=5)

        self.refresh_switch = ctk.CTkSwitch(top_frame, text="Enable", command=self.toggle_refresh)
        self.refresh_switch.pack(side="left", padx=5)

        ctk.CTkLabel(top_frame, text="Interval (sec):").pack(side="left", padx=5)
        self.time_entry = ctk.CTkEntry(top_frame, textvariable=self.time_var, width=60)
        self.time_entry.pack(side="left", padx=5)

        # Manual refresh button
        ctk.CTkButton(top_frame, text="Refresh Now", command=self.refresh_all_data, width=80).pack(side="left", padx=5)

        # Status display
        self.status_label = ctk.CTkLabel(top_frame, text="Ready", font=ctk.CTkFont(size=12))
        self.status_label.pack(side="right", padx=5)

        # Debug button
        debug_button = ctk.CTkButton(top_frame, text="Debug", width=60, command=self.debug_status)
        debug_button.pack(side="right", padx=5)

        # Bottom row - Auto SL to BE controls
        bottom_frame = ctk.CTkFrame(self.control_frame)
        bottom_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(bottom_frame, text="Auto SL to BE:", font=ctk.CTkFont(weight="bold")).pack(side="left", padx=5)

        # Create toggle switches dynamically based on config
        self.auto_be_switches = {}
        for group_key, group_config in self.config.order_groups.items():
            switch = ctk.CTkSwitch(
                bottom_frame,
                text=group_config["display_name"],
                command=lambda gk=group_key: self.toggle_auto_be(gk)
            )
            switch.pack(side="left", padx=5)

            # Set initial state based on config
            if group_config["default_sl_enabled"]:
                switch.select()

            self.auto_be_switches[group_key] = switch

        # Add a visual indicator for enabled auto BE
        self.auto_be_status_label = ctk.CTkLabel(bottom_frame, text="", font=ctk.CTkFont(size=10))
        self.auto_be_status_label.pack(side="right", padx=5)

        # Third row - Auto Moving TP controls
        tp_frame = ctk.CTkFrame(self.control_frame)
        tp_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(tp_frame, text="Auto Moving TP:", font=ctk.CTkFont(weight="bold")).pack(side="left", padx=5)

        # Create TP toggle switches dynamically based on config
        self.auto_tp_switches = {}
        for group_key, group_config in self.config.order_groups.items():
            switch = ctk.CTkSwitch(
                tp_frame,
                text=group_config["display_name"],
                command=lambda gk=group_key: self.toggle_auto_tp(gk)
            )
            switch.pack(side="left", padx=5)

            # Set initial state based on config
            if group_config["default_tp_enabled"]:
                switch.select()

            self.auto_tp_switches[group_key] = switch

        # Add a visual indicator for enabled auto TP
        self.auto_tp_status_label = ctk.CTkLabel(tp_frame, text="", font=ctk.CTkFont(size=10))
        self.auto_tp_status_label.pack(side="right", padx=5)

        # Fourth row - Schedule Check controls
        schedule_frame = ctk.CTkFrame(self.control_frame)
        schedule_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(schedule_frame, text="Schedule Check:", font=ctk.CTkFont(weight="bold")).pack(side="left", padx=5)

        # Schedule enable/disable switch
        self.schedule_switch = ctk.CTkSwitch(schedule_frame, text="Enable", command=self.toggle_schedule)
        self.schedule_switch.pack(side="left", padx=5)

        # Schedule type dropdown
        ctk.CTkLabel(schedule_frame, text="Type:").pack(side="left", padx=5)
        self.schedule_type_var = ctk.StringVar(value=self.schedule_type)
        self.schedule_type_dropdown = ctk.CTkOptionMenu(
            schedule_frame,
            values=["custom", "hourly", "daily", "weekly", "monthly"],
            variable=self.schedule_type_var,
            command=self.on_schedule_type_change,
            width=100
        )
        self.schedule_type_dropdown.pack(side="left", padx=5)

        # Dynamic schedule input frame
        self.schedule_input_frame = ctk.CTkFrame(schedule_frame)
        self.schedule_input_frame.pack(side="left", padx=5)

        # Build initial schedule inputs
        self.build_schedule_inputs()

        # Schedule status label
        self.schedule_status_label = ctk.CTkLabel(schedule_frame, text="", font=ctk.CTkFont(size=10))
        self.schedule_status_label.pack(side="right", padx=5)

    def build_dynamic_subtabs(self):
        """Build all subtabs dynamically based on config"""
        for group_key, group_config in self.config.order_groups.items():
            if group_config.get("has_subtab", False):
                subtab_type = group_config.get("subtab_type", "individual")

                if subtab_type == "combined":
                    self._build_combined_subtab(group_key, group_config)
                elif subtab_type == "grouped":
                    self._build_grouped_subtab(group_key, group_config)
                elif subtab_type == "individual":
                    self._build_individual_subtab(group_key, group_config)

    def _build_combined_subtab(self, group_key, group_config):
        """Build combined view subtab (positions + pending orders)"""
        tab = self.group_tabs[group_key]

        # Create frames for positions and pending orders
        pos_frame = ctk.CTkFrame(tab)
        pos_frame.pack(fill="both", expand=True, padx=5, pady=5)

        ctk.CTkLabel(pos_frame, text="Open Positions", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # Positions treeview
        positions_tree = ttk.Treeview(pos_frame, columns=("Symbol", "Type", "Volume", "Entry", "Current", "SL", "TP", "Profit", "Comment"), show="headings", height=8)

        # Configure columns
        columns = [("Symbol", 80), ("Type", 60), ("Volume", 70), ("Entry", 80), ("Current", 80), ("SL", 80), ("TP", 80), ("Profit", 80), ("Comment", 150)]
        for col, width in columns:
            positions_tree.heading(col, text=col)
            positions_tree.column(col, width=width, anchor="center")

        # Add scrollbar for positions
        pos_scrollbar = ttk.Scrollbar(pos_frame, orient="vertical", command=positions_tree.yview)
        positions_tree.configure(yscrollcommand=pos_scrollbar.set)

        positions_tree.pack(side="left", fill="both", expand=True)
        pos_scrollbar.pack(side="right", fill="y")

        # Pending orders frame
        pending_frame = ctk.CTkFrame(tab)
        pending_frame.pack(fill="both", expand=True, padx=5, pady=5)

        ctk.CTkLabel(pending_frame, text="Pending Orders", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # Pending orders treeview
        pending_tree = ttk.Treeview(pending_frame, columns=("Symbol", "Type", "Volume", "Entry", "SL", "TP", "Comment"), show="headings", height=8)

        # Configure columns for pending orders
        pending_columns = [("Symbol", 80), ("Type", 80), ("Volume", 70), ("Entry", 80), ("SL", 80), ("TP", 80), ("Comment", 150)]
        for col, width in pending_columns:
            pending_tree.heading(col, text=col)
            pending_tree.column(col, width=width, anchor="center")

        # Add scrollbar for pending orders
        pending_scrollbar = ttk.Scrollbar(pending_frame, orient="vertical", command=pending_tree.yview)
        pending_tree.configure(yscrollcommand=pending_scrollbar.set)

        pending_tree.pack(side="left", fill="both", expand=True)
        pending_scrollbar.pack(side="right", fill="y")

        # Store widgets for later access
        self.group_widgets[group_key] = {
            "positions_tree": positions_tree,
            "pending_tree": pending_tree
        }

    def _build_grouped_subtab(self, group_key, group_config):
        """Build grouped view subtab (grouped by ID)"""
        tab = self.group_tabs[group_key]

        # Groups list frame
        list_frame = ctk.CTkFrame(tab)
        list_frame.pack(fill="both", expand=True, padx=5, pady=5)

        group_name = group_config["display_name"]
        ctk.CTkLabel(list_frame, text=f"{group_name} Groups", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # Groups treeview
        groups_tree = ttk.Treeview(list_frame, columns=("ID", "TP_Count", "Total_Volume", "Total_Profit", "Actions"), show="headings", height=15)

        # Configure columns for groups
        columns = [("ID", 120), ("TP_Count", 80), ("Total_Volume", 100), ("Total_Profit", 100), ("Actions", 100)]
        for col, width in columns:
            groups_tree.heading(col, text=col)
            groups_tree.column(col, width=width, anchor="center")

        # Add scrollbar for groups
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=groups_tree.yview)
        groups_tree.configure(yscrollcommand=scrollbar.set)

        groups_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind double-click event to close group
        groups_tree.bind("<Double-1>", lambda event: self._on_group_double_click(event, group_key))

        # Close button for selected group
        button_frame = ctk.CTkFrame(tab)
        button_frame.pack(fill="x", padx=5, pady=5)

        ctk.CTkButton(button_frame, text=f"Close Selected {group_name} Group",
                     fg_color="red", hover_color="darkred",
                     command=lambda: self._close_selected_group(group_key)).pack(side="right", padx=5)

        # Store widgets for later access
        self.group_widgets[group_key] = {
            "groups_tree": groups_tree
        }

    def _build_individual_subtab(self, group_key, group_config):
        """Build individual view subtab (individual orders without grouping)"""
        tab = self.group_tabs[group_key]

        # Orders list frame
        list_frame = ctk.CTkFrame(tab)
        list_frame.pack(fill="both", expand=True, padx=5, pady=5)

        group_name = group_config["display_name"]
        prefix = group_config["prefix"]
        ctk.CTkLabel(list_frame, text=f"{group_name} Orders (All orders starting with {prefix})", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # Orders treeview
        orders_tree = ttk.Treeview(list_frame, columns=("Symbol", "Type", "Volume", "Entry", "Current", "SL", "TP", "Profit", "Comment"), show="headings", height=15)

        # Configure columns for orders
        columns = [("Symbol", 80), ("Type", 60), ("Volume", 70), ("Entry", 80), ("Current", 80), ("SL", 80), ("TP", 80), ("Profit", 80), ("Comment", 150)]
        for col, width in columns:
            orders_tree.heading(col, text=col)
            orders_tree.column(col, width=width, anchor="center")

        # Add scrollbar for orders
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=orders_tree.yview)
        orders_tree.configure(yscrollcommand=scrollbar.set)

        orders_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Close button for selected order
        button_frame = ctk.CTkFrame(tab)
        button_frame.pack(fill="x", padx=5, pady=5)

        ctk.CTkButton(button_frame, text=f"Close Selected {group_name} Order",
                     fg_color="red", hover_color="darkred",
                     command=lambda: self._close_selected_order(group_key)).pack(side="right", padx=5)

        # Store widgets for later access
        self.group_widgets[group_key] = {
            "orders_tree": orders_tree
        }
 
    # Auto SL to BE toggle methods
    def toggle_auto_be(self, group_key):
        """Toggle auto SL to BE for specific group"""
        self.auto_be_settings[group_key] = self.auto_be_switches[group_key].get()
        group_name = self.config.order_groups[group_key]["display_name"]

        # Use debug logger for toggle action
        self.debug_logger.log_toggle_action("Auto SL to BE", group_name, self.auto_be_settings[group_key])

        self._manage_loop_for_auto_be()
        self.update_status_display()

    def toggle_auto_tp(self, group_key):
        """Toggle auto Moving TP for specific group"""
        self.auto_tp_settings[group_key] = self.auto_tp_switches[group_key].get()
        group_name = self.config.order_groups[group_key]["display_name"]

        # Use debug logger for toggle action
        self.debug_logger.log_toggle_action("Auto Moving TP", group_name, self.auto_tp_settings[group_key])

        self._manage_loop_for_auto_be()  # Use same loop management
        self.update_status_display()

    def _manage_loop_for_auto_be(self):
        """Start or stop loop based on auto BE settings"""
        if self.has_auto_be_enabled():
            # Start loop if any auto BE is enabled
            if not self.loop_running:
                self.start_loop()
        else:
            # Stop loop only if auto refresh is also disabled
            if not self.auto_refresh_enabled:
                self.stop_loop()

    def update_status_display(self):
        """Update the status display with current settings"""
        # Show separate status for refresh and auto BE
        refresh_status = "ON" if self.auto_refresh_enabled else "OFF"
        loop_status = "RUNNING" if self.loop_running else "STOPPED"

        be_statuses = []
        tp_statuses = []
        for group_key, enabled in self.auto_be_settings.items():
            if enabled:
                be_statuses.append(self.config.order_groups[group_key]["display_name"])

        for group_key, enabled in self.auto_tp_settings.items():
            if enabled:
                tp_statuses.append(self.config.order_groups[group_key]["display_name"])

        be_status = "+".join(be_statuses) if be_statuses else "OFF"
        tp_status = "+".join(tp_statuses) if tp_statuses else "OFF"
        status_text = f"Refresh: {refresh_status} | Loop: {loop_status} | Auto BE: {be_status} | Auto TP: {tp_status}"

        if hasattr(self, 'status_label'):
            self.status_label.configure(text=status_text)

        # Update auto BE status label with color coding
        if hasattr(self, 'auto_be_status_label'):
            if be_statuses:
                self.auto_be_status_label.configure(text=f"✅ Active: {be_status}", text_color="lime")
            else:
                self.auto_be_status_label.configure(text="❌ Inactive", text_color="red")

        # Update auto TP status label with color coding
        if hasattr(self, 'auto_tp_status_label'):
            if tp_statuses:
                self.auto_tp_status_label.configure(text=f"✅ Active: {tp_status}", text_color="lime")
            else:
                self.auto_tp_status_label.configure(text="❌ Inactive", text_color="red")

    def debug_status(self):
        """Debug method to check current status"""
        debug_info = [
            f"Loop Running: {self.loop_running}",
            f"Refresh Switch: {self.refresh_switch.get() if hasattr(self, 'refresh_switch') else 'N/A'}",
            f"Time Interval: {self.time_var.get()}",
        ]

        # Add auto BE status for each group
        for group_key, enabled in self.auto_be_settings.items():
            group_name = self.config.order_groups[group_key]["display_name"]
            debug_info.append(f"Auto BE {group_name}: {enabled}")

        # Add auto TP status for each group
        for group_key, enabled in self.auto_tp_settings.items():
            group_name = self.config.order_groups[group_key]["display_name"]
            debug_info.append(f"Auto TP {group_name}: {enabled}")

        debug_message = "🔍 DEBUG STATUS:\n" + "\n".join(debug_info)
        self.util.add_status_frame(debug_message)
        print(debug_message)  # Also print to console

    # Data refresh and management methods
    def toggle_refresh(self):
        """Toggle auto refresh on/off"""
        self.auto_refresh_enabled = self.refresh_switch.get()

        # Start or manage the loop based on what's enabled
        if self.auto_refresh_enabled or self.has_auto_be_enabled():
            self.start_loop()
        else:
            self.stop_loop()

        # Use debug logger for toggle action
        self.debug_logger.log_toggle_action("Auto Refresh", "Data", self.auto_refresh_enabled)
        self.update_status_display()

    # Schedule functionality methods
    def load_schedule_settings(self):
        """Load schedule settings from JSON file"""
        try:
            if os.path.exists(self.schedule_settings_file):
                with open(self.schedule_settings_file, 'r') as f:
                    settings = json.load(f)
                    self.schedule_enabled = settings.get("enabled", False)
                    self.schedule_type = settings.get("type", "custom")
                    self.schedule_custom_minutes = settings.get("custom_minutes", 60)
                    self.schedule_daily_time = settings.get("daily_time", "09:00")
                    self.schedule_weekly_day = settings.get("weekly_day", "Monday")
                    self.schedule_weekly_time = settings.get("weekly_time", "09:00")
                    self.schedule_monthly_day = settings.get("monthly_day", 1)
                    self.schedule_monthly_time = settings.get("monthly_time", "09:00")
                    self.last_schedule_check = settings.get("last_check")
        except Exception as e:
            self.util.add_status_frame(f"⚠️ Error loading schedule settings: {e}", "yellow")

    def save_schedule_settings(self):
        """Save schedule settings to JSON file"""
        try:
            settings = {
                "enabled": self.schedule_enabled,
                "type": self.schedule_type,
                "custom_minutes": self.schedule_custom_minutes,
                "daily_time": self.schedule_daily_time,
                "weekly_day": self.schedule_weekly_day,
                "weekly_time": self.schedule_weekly_time,
                "monthly_day": self.schedule_monthly_day,
                "monthly_time": self.schedule_monthly_time,
                "last_check": self.last_schedule_check
            }
            with open(self.schedule_settings_file, 'w') as f:
                json.dump(settings, f, indent=2)
        except Exception as e:
            self.util.add_status_frame(f"⚠️ Error saving schedule settings: {e}", "yellow")

    def toggle_schedule(self):
        """Toggle schedule check on/off"""
        self.schedule_enabled = self.schedule_switch.get()

        if self.schedule_enabled:
            self.start_schedule_check()
        else:
            self.stop_schedule_check()

        self.save_schedule_settings()
        self.update_schedule_status()

    def on_schedule_type_change(self, selected_type):
        """Handle schedule type change"""
        self.schedule_type = selected_type
        self.build_schedule_inputs()
        self.save_schedule_settings()

    def build_schedule_inputs(self):
        """Build schedule input widgets based on selected type"""
        # Clear existing widgets
        for widget in self.schedule_input_frame.winfo_children():
            widget.destroy()

        if self.schedule_type == "custom":
            ctk.CTkLabel(self.schedule_input_frame, text="Minutes:").pack(side="left", padx=2)
            self.schedule_custom_entry = ctk.CTkEntry(self.schedule_input_frame, width=60)
            self.schedule_custom_entry.pack(side="left", padx=2)
            self.schedule_custom_entry.insert(0, str(self.schedule_custom_minutes))
            self.schedule_custom_entry.bind("<KeyRelease>", self.on_custom_minutes_change)

        elif self.schedule_type == "hourly":
            ctk.CTkLabel(self.schedule_input_frame, text="Every hour").pack(side="left", padx=2)

        elif self.schedule_type == "daily":
            ctk.CTkLabel(self.schedule_input_frame, text="Time:").pack(side="left", padx=2)
            self.schedule_daily_entry = ctk.CTkEntry(self.schedule_input_frame, width=60)
            self.schedule_daily_entry.pack(side="left", padx=2)
            self.schedule_daily_entry.insert(0, self.schedule_daily_time)
            self.schedule_daily_entry.bind("<KeyRelease>", self.on_daily_time_change)

        elif self.schedule_type == "weekly":
            ctk.CTkLabel(self.schedule_input_frame, text="Day:").pack(side="left", padx=2)
            self.schedule_weekly_day_var = ctk.StringVar(value=self.schedule_weekly_day)
            weekly_day_dropdown = ctk.CTkOptionMenu(
                self.schedule_input_frame,
                values=["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
                variable=self.schedule_weekly_day_var,
                command=self.on_weekly_day_change,
                width=80
            )
            weekly_day_dropdown.pack(side="left", padx=2)

            ctk.CTkLabel(self.schedule_input_frame, text="Time:").pack(side="left", padx=2)
            self.schedule_weekly_time_entry = ctk.CTkEntry(self.schedule_input_frame, width=60)
            self.schedule_weekly_time_entry.pack(side="left", padx=2)
            self.schedule_weekly_time_entry.insert(0, self.schedule_weekly_time)
            self.schedule_weekly_time_entry.bind("<KeyRelease>", self.on_weekly_time_change)

        elif self.schedule_type == "monthly":
            ctk.CTkLabel(self.schedule_input_frame, text="Day:").pack(side="left", padx=2)
            self.schedule_monthly_day_entry = ctk.CTkEntry(self.schedule_input_frame, width=40)
            self.schedule_monthly_day_entry.pack(side="left", padx=2)
            self.schedule_monthly_day_entry.insert(0, str(self.schedule_monthly_day))
            self.schedule_monthly_day_entry.bind("<KeyRelease>", self.on_monthly_day_change)

            ctk.CTkLabel(self.schedule_input_frame, text="Time:").pack(side="left", padx=2)
            self.schedule_monthly_time_entry = ctk.CTkEntry(self.schedule_input_frame, width=60)
            self.schedule_monthly_time_entry.pack(side="left", padx=2)
            self.schedule_monthly_time_entry.insert(0, self.schedule_monthly_time)
            self.schedule_monthly_time_entry.bind("<KeyRelease>", self.on_monthly_time_change)

    # Event handlers for schedule inputs
    def on_custom_minutes_change(self, event):
        """Handle custom minutes input change"""
        try:
            self.schedule_custom_minutes = int(self.schedule_custom_entry.get())
            self.save_schedule_settings()
        except ValueError:
            pass  # Ignore invalid input

    def on_daily_time_change(self, event):
        """Handle daily time input change"""
        self.schedule_daily_time = self.schedule_daily_entry.get()
        self.save_schedule_settings()

    def on_weekly_day_change(self, selected_day):
        """Handle weekly day change"""
        self.schedule_weekly_day = selected_day
        self.save_schedule_settings()

    def on_weekly_time_change(self, event):
        """Handle weekly time input change"""
        self.schedule_weekly_time = self.schedule_weekly_time_entry.get()
        self.save_schedule_settings()

    def on_monthly_day_change(self, event):
        """Handle monthly day input change"""
        try:
            day = int(self.schedule_monthly_day_entry.get())
            if 1 <= day <= 31:
                self.schedule_monthly_day = day
                self.save_schedule_settings()
        except ValueError:
            pass  # Ignore invalid input

    def on_monthly_time_change(self, event):
        """Handle monthly time input change"""
        self.schedule_monthly_time = self.schedule_monthly_time_entry.get()
        self.save_schedule_settings()

    def start_schedule_check(self):
        """Start the schedule check thread"""
        if not self.schedule_running:
            self.schedule_running = True
            self.schedule_thread = threading.Thread(target=self.schedule_check_loop, daemon=True)
            self.schedule_thread.start()
            self.util.add_status_frame("🕒 Schedule check started", "green")

    def stop_schedule_check(self):
        """Stop the schedule check thread"""
        self.schedule_running = False
        if self.schedule_thread and self.schedule_thread.is_alive():
            self.schedule_thread.join(timeout=2.0)
        self.util.add_status_frame("🕒 Schedule check stopped", "yellow")

    def schedule_check_loop(self):
        """Main schedule check loop"""
        while self.schedule_running:
            try:
                if self.should_run_scheduled_check():
                    self.run_scheduled_check()
                    self.last_schedule_check = datetime.now().isoformat()
                    self.save_schedule_settings()

                # Check every minute
                time.sleep(60)

            except Exception as e:
                self.util.add_status_frame(f"❌ Schedule check error: {e}", "red")
                time.sleep(60)

    def should_run_scheduled_check(self):
        """Determine if scheduled check should run based on schedule type"""
        now = datetime.now()

        if not self.last_schedule_check:
            return True

        try:
            last_check = datetime.fromisoformat(self.last_schedule_check)
        except:
            return True

        if self.schedule_type == "custom":
            return (now - last_check).total_seconds() >= (self.schedule_custom_minutes * 60)

        elif self.schedule_type == "hourly":
            return (now - last_check).total_seconds() >= 3600  # 1 hour

        elif self.schedule_type == "daily":
            target_time = datetime.strptime(self.schedule_daily_time, "%H:%M").time()
            today_target = datetime.combine(now.date(), target_time)

            # If we've passed today's target time and haven't checked today
            if now >= today_target and last_check.date() < now.date():
                return True

        elif self.schedule_type == "weekly":
            weekdays = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
            target_weekday = weekdays.index(self.schedule_weekly_day)
            target_time = datetime.strptime(self.schedule_weekly_time, "%H:%M").time()

            # If today is the target weekday and we've passed the target time
            if now.weekday() == target_weekday:
                today_target = datetime.combine(now.date(), target_time)
                if now >= today_target and last_check.date() < now.date():
                    return True

        elif self.schedule_type == "monthly":
            target_time = datetime.strptime(self.schedule_monthly_time, "%H:%M").time()

            # Check if today is the target day of the month
            if now.day == self.schedule_monthly_day:
                today_target = datetime.combine(now.date(), target_time)
                if now >= today_target and last_check.date() < now.date():
                    return True

        return False

    def run_scheduled_check(self):
        """Execute the scheduled check - refresh all data"""
        self.util.add_status_frame("🕒 Running scheduled check...", "cyan")
        self.refresh_all_data()
        self.update_schedule_status()

    def update_schedule_status(self):
        """Update schedule status display"""
        if self.schedule_enabled:
            if self.last_schedule_check:
                try:
                    last_check = datetime.fromisoformat(self.last_schedule_check)
                    time_str = last_check.strftime("%H:%M")
                    self.schedule_status_label.configure(text=f"Last: {time_str}")
                except:
                    self.schedule_status_label.configure(text="Active")
            else:
                self.schedule_status_label.configure(text="Active")
        else:
            self.schedule_status_label.configure(text="Disabled")

    # Market monitoring methods for auto-restart functionality
    def start_market_monitor(self):
        """Start market monitoring thread to detect when market reopens"""
        if not self.market_monitor_running:
            self.market_monitor_running = True
            self.market_monitor_thread = threading.Thread(target=self.market_monitor_loop, daemon=True)
            self.market_monitor_thread.start()
            self.util.add_status_frame("📊 Market monitor started", "cyan")

    def stop_market_monitor(self):
        """Stop market monitoring thread"""
        self.market_monitor_running = False
        if self.market_monitor_thread and self.market_monitor_thread.is_alive():
            self.market_monitor_thread.join(timeout=2.0)

    def market_monitor_loop(self):
        """Monitor market status and restart refresh loop when market reopens"""
        while self.market_monitor_running:
            try:
                # Check market status every 5 minutes
                time.sleep(300)  # 5 minutes

                if self.is_market_open():
                    if self.market_was_closed:
                        # Market just reopened, restart refresh loop if needed
                        self.market_was_closed = False
                        if (self.auto_refresh_enabled or self.has_auto_be_enabled()) and not self.loop_running:
                            self.util.add_status_frame("📈 Market reopened - restarting refresh loop", "green")
                            self.start_loop()
                else:
                    self.market_was_closed = True

            except Exception as e:
                self.util.add_status_frame(f"⚠️ Market monitor error: {e}", "yellow")
                time.sleep(300)  # Wait 5 minutes before retry

    def is_market_open(self):
        """Check if market is currently open by testing MT5 connection and symbol availability"""
        try:
            # Try to initialize MT5
            if not mt5.initialize():
                return False

            # Try to get tick data for a common symbol (EURUSD)
            test_symbols = ["EURUSD", "XAUUSD", "GBPUSD", "USDJPY"]

            for symbol in test_symbols:
                try:
                    tick = mt5.symbol_info_tick(symbol)
                    if tick and tick.time > 0:
                        # If we can get recent tick data, market is likely open
                        current_time = time.time()
                        tick_age = current_time - tick.time

                        # If tick is less than 10 minutes old, consider market open
                        if tick_age < 600:  # 10 minutes
                            return True
                except:
                    continue

            return False

        except Exception:
            return False

    def has_auto_be_enabled(self):
        """Check if any auto BE or TP mode is enabled"""
        return any(self.auto_be_settings.values()) or any(self.auto_tp_settings.values())

    def check_mt5_health(self):
        """Check MT5 connection health and implement circuit breaker"""
        try:
            # Quick connection test
            if not mt5.initialize():
                self.mt5_error_count += 1
                return False

            # Reset error count on successful connection
            self.mt5_error_count = 0
            self.last_successful_operation = time.time()
            return True

        except Exception as e:
            self.mt5_error_count += 1
            self.util.add_status_frame(f"❌ MT5 health check failed: {e}", "red")
            return False

    def should_skip_mt5_operations(self):
        """Determine if MT5 operations should be skipped due to errors"""
        if self.mt5_error_count >= self.max_mt5_errors:
            time_since_last_success = time.time() - self.last_successful_operation
            if time_since_last_success < 300:  # 5 minutes cooldown
                return True
            else:
                # Reset after cooldown period
                self.mt5_error_count = 0
        return False

    def start_loop(self):
        """Start the refresh loop"""
        if not self.loop_running:
            self.loop_running = True
            self.thread1 = threading.Thread(target=self.refresh_loop, daemon=True)
            self.thread1.start()
            self.debug_logger.log_info("🟢 Orders processing loop started")
        self.update_status_display()

    def stop_loop(self):
        """Stop the loop only if both auto refresh and auto BE are disabled"""
        if not self.auto_refresh_enabled and not self.has_auto_be_enabled():
            self.loop_running = False
            self.debug_logger.log_info("🔴 All auto functions stopped")
        else:
            # Keep loop running if any auto function is enabled
            reasons = []
            if self.auto_refresh_enabled:
                reasons.append("auto refresh")
            if self.has_auto_be_enabled():
                reasons.append("auto BE/TP")
            self.debug_logger.log_info(f"🟡 Loop continues for: {', '.join(reasons)}")

        self.update_status_display()

    def refresh_loop(self):
        """Main refresh loop with improved error handling and connection management"""
        consecutive_errors = 0
        max_consecutive_errors = 5
        loop_count = 0

        try:
            while self.loop_running:
                try:
                    # Check if MT5 is still connected before proceeding
                    if not mt5.initialize():
                        self.util.add_status_frame("⚠️ MT5 connection lost, attempting to reconnect...", "yellow")
                        time.sleep(10)  # Wait before retry
                        consecutive_errors += 1
                        if consecutive_errors >= max_consecutive_errors:
                            # Check if market is closed before stopping permanently
                            if not self.is_market_open():
                                self.util.add_status_frame(f"📉 Market appears closed - stopping refresh loop and starting market monitor", "yellow")
                                self.market_was_closed = True
                                self.start_market_monitor()
                            else:
                                self.util.add_status_frame(f"❌ Too many connection errors ({consecutive_errors}), stopping refresh loop", "red")
                            self.loop_running = False
                            break
                        continue

                    # Reset error counter on successful connection
                    consecutive_errors = 0

                    # Perform operations based on what's enabled
                    if self.auto_refresh_enabled:
                        self.refresh_all_data()

                    # Always process auto BE if any mode is enabled
                    if self.has_auto_be_enabled():
                        self.process_auto_be()

                    # Periodic memory cleanup (every 50 loops)
                    loop_count += 1
                    if loop_count % 50 == 0:
                        self.util.cleanup_memory()
                        loop_count = 0  # Reset counter

                    # Adjust sleep time based on what's enabled
                    if self.auto_refresh_enabled:
                        # Use user-defined interval for refresh
                        sleep_time = max(self.time_var.get(), 10)  # Minimum 10 seconds
                    else:
                        # Only auto BE running - use longer interval to save resources
                        sleep_time = max(self.time_var.get() * 2, 30)  # Minimum 30 seconds for BE only

                    time.sleep(sleep_time)

                except Exception as e:
                    consecutive_errors += 1
                    self.util.add_status_frame(f"❌ Refresh loop error ({consecutive_errors}/{max_consecutive_errors}): {e}", "red")

                    if consecutive_errors >= max_consecutive_errors:
                        # Check if market is closed before stopping permanently
                        if not self.is_market_open():
                            self.util.add_status_frame(f"📉 Market appears closed - stopping refresh loop and starting market monitor", "yellow")
                            self.market_was_closed = True
                            self.start_market_monitor()
                        else:
                            self.util.add_status_frame(f"❌ Too many consecutive errors, stopping refresh loop", "red")
                        self.loop_running = False
                        break

                    # Wait longer after errors
                    time.sleep(30)

        except Exception as e:
            self.util.add_status_frame(f"❌ Critical refresh loop error: {e}", "red")
        finally:
            self.util.add_status_frame("🔴 Refresh loop stopped", "yellow")

    def process_auto_be(self):
        """Process auto SL to BE for enabled filters with circuit breaker"""
        try:
            # Skip if no auto BE is enabled
            if not self.has_auto_be_enabled():
                return

            # Check if we should skip MT5 operations due to errors
            if self.should_skip_mt5_operations():
                return

            # Check MT5 health
            if not self.check_mt5_health():
                return

            # Get all positions for processing safely
            positions = self.util.safe_mt5_call(mt5.positions_get)
            if not positions:
                self.debug_logger.debug_print("No positions found for auto BE/TP processing")
                return

            symbols = set(pos.symbol for pos in positions)
            if not symbols:
                return

            self.debug_logger.debug_print(f"Processing auto BE/TP for {len(symbols)} symbols: {symbols}")

            # Process each symbol with error handling
            for symbol in symbols:
                try:
                    # Get positions for this symbol to check comments
                    symbol_positions = [pos for pos in positions if pos.symbol == symbol]
                    if not symbol_positions:
                        continue

                    self.debug_logger.debug_print(f"Processing {len(symbol_positions)} positions for {symbol}")

                    # Process each group that has either SL or TP enabled
                    processed_groups = set()

                    # Process groups with SL enabled (with or without TP)
                    for group_key, sl_enabled in self.auto_be_settings.items():
                        if sl_enabled:
                            group_config = self.config.order_groups[group_key]
                            prefix = group_config["prefix"]

                            # Check if any positions match this prefix
                            matching_positions = [pos for pos in symbol_positions if pos.comment.startswith(prefix)]
                            if not matching_positions:
                                self.debug_logger.debug_print(f"No positions found for prefix '{prefix}' in {symbol}")
                                continue

                            # Check if TP is also enabled for this group
                            is_moving_tp = self.auto_tp_settings.get(group_key, False)

                            self.debug_logger.debug_print(f"Processing {len(matching_positions)} positions for {prefix} (SL: {sl_enabled}, TP: {is_moving_tp})")

                            result = self.util.update_SL_to_BE_by_point(symbol, prefix, is_moving_tp)
                            if result and result.get("All_FT", 0) > 0:
                                action_type = "SL+TP" if is_moving_tp else "SL to BE"
                                self.debug_logger.debug_print(f"Processed {result['All_FT']} {prefix} positions for {action_type}")

                            processed_groups.add(group_key)

                    # Process groups with only TP enabled (no SL)
                    for group_key, tp_enabled in self.auto_tp_settings.items():
                        if tp_enabled and group_key not in processed_groups:
                            group_config = self.config.order_groups[group_key]
                            prefix = group_config["prefix"]

                            # Check if any positions match this prefix
                            matching_positions = [pos for pos in symbol_positions if pos.comment.startswith(prefix)]
                            if not matching_positions:
                                continue

                            self.debug_logger.debug_print(f"Processing {len(matching_positions)} positions for {prefix} (TP only)")

                            # Only moving TP, no SL to BE
                            result = self.util.update_SL_to_BE_by_point(symbol, prefix, True)
                            if result and result.get("All_FT", 0) > 0:
                                self.debug_logger.debug_print(f"Processed {result['All_FT']} {prefix} positions for TP only")

                except Exception as e:
                    self.util.add_status_frame(f"❌ Auto BE/TP error for {symbol}: {e}", "yellow")
                    self.debug_logger.log_error(e, f"process_auto_be for {symbol}")
                    continue

        except Exception as e:
            self.mt5_error_count += 1
            self.util.add_status_frame(f"❌ Auto SL to BE error: {e}", "red")
            self.debug_logger.log_error(e, "process_auto_be")

    def refresh_all_data(self):
        """Refresh all order data with improved error handling"""
        try:
            # Check MT5 connection first
            if not mt5.initialize():
                self.util.add_status_frame("❌ MT5 not connected - cannot refresh data", "red")
                return

            # Refresh data with individual error handling
            # Refresh all subtabs dynamically
            try:
                self.refresh_all_subtabs()
            except Exception as e:
                self.util.add_status_frame(f"⚠️ Subtabs refresh error: {e}", "yellow")

            # Update status with last refresh time (thread-safe)
            current_time = time.strftime('%H:%M:%S')
            if hasattr(self, 'status_label'):
                def update_status():
                    try:
                        current_text = self.status_label.cget("text")
                        base_text = current_text.split(" | Last:")[0] if " | Last:" in current_text else current_text
                        self.status_label.configure(text=f"{base_text} | Last: {current_time}")
                    except:
                        pass

                # Schedule status update on main thread
                try:
                    self.status_label.after(0, update_status)
                except:
                    update_status()

        except Exception as e:
            self.util.add_status_frame(f"❌ Data refresh error: {e}", "red")

    def refresh_all_subtabs(self):
        """Refresh all subtabs dynamically based on config"""
        for group_key, group_config in self.config.order_groups.items():
            if group_config.get("has_subtab", False):
                subtab_type = group_config.get("subtab_type", "individual")

                try:
                    if subtab_type == "combined":
                        self._refresh_combined_subtab(group_key, group_config)
                    elif subtab_type == "grouped":
                        self._refresh_grouped_subtab(group_key, group_config)
                    elif subtab_type == "individual":
                        self._refresh_individual_subtab(group_key, group_config)
                except Exception as e:
                    group_name = group_config.get("display_name", group_key)
                    self.util.add_status_frame(f"⚠️ {group_name} refresh error: {e}", "yellow")

    def _refresh_combined_subtab(self, group_key, group_config):
        """Refresh combined view subtab (positions + pending orders)"""
        widgets = self.group_widgets.get(group_key, {})
        positions_tree = widgets.get("positions_tree")
        pending_tree = widgets.get("pending_tree")

        if positions_tree:
            # Clear existing positions data
            for item in positions_tree.get_children():
                positions_tree.delete(item)

            # Get positions
            positions = mt5.positions_get() if mt5.initialize() else []
            if positions:
                for pos in positions:
                    positions_tree.insert("", "end", values=(
                        pos.symbol,
                        "BUY" if pos.type == 0 else "SELL",
                        f"{pos.volume:.2f}",
                        f"{pos.price_open:.5f}",
                        f"{pos.price_current:.5f}",
                        f"{pos.sl:.5f}" if pos.sl > 0 else "None",
                        f"{pos.tp:.5f}" if pos.tp > 0 else "None",
                        f"{pos.profit:.2f}",
                        pos.comment
                    ))

        if pending_tree:
            # Clear existing pending orders data
            for item in pending_tree.get_children():
                pending_tree.delete(item)

            # Get pending orders
            orders = mt5.orders_get() if mt5.initialize() else []
            if orders:
                for order in orders:
                    order_type_name = "Buy Limit" if order.type == mt5.ORDER_TYPE_BUY_LIMIT else \
                                     "Sell Limit" if order.type == mt5.ORDER_TYPE_SELL_LIMIT else \
                                     "Buy Stop" if order.type == mt5.ORDER_TYPE_BUY_STOP else \
                                     "Sell Stop" if order.type == mt5.ORDER_TYPE_SELL_STOP else \
                                     f"Type {order.type}"

                    pending_tree.insert("", "end", values=(
                        order.symbol,
                        order_type_name,
                        f"{order.volume_initial:.2f}",
                        f"{order.price_open:.5f}",
                        f"{order.sl:.5f}" if order.sl > 0 else "None",
                        f"{order.tp:.5f}" if order.tp > 0 else "None",
                        order.comment
                    ))

    def _refresh_grouped_subtab(self, group_key, group_config):
        """Refresh grouped view subtab (grouped by ID)"""
        widgets = self.group_widgets.get(group_key, {})
        groups_tree = widgets.get("groups_tree")

        if not groups_tree:
            return

        # Clear existing data
        for item in groups_tree.get_children():
            groups_tree.delete(item)

        prefix = group_config["prefix"]
        groups = defaultdict(lambda: {'positions': [], 'pending': [], 'tp_count': 0, 'total_volume': 0, 'total_profit': 0})

        # Process positions
        positions = mt5.positions_get() if mt5.initialize() else []
        if positions:
            for pos in positions:
                group_id = self._extract_group_id(pos.comment, prefix)
                if group_id:
                    groups[group_id]['positions'].append(pos)
                    groups[group_id]['total_volume'] += pos.volume
                    groups[group_id]['total_profit'] += pos.profit
                    groups[group_id]['tp_count'] += 1

        # Process pending orders
        orders = mt5.orders_get() if mt5.initialize() else []
        if orders:
            for order in orders:
                group_id = self._extract_group_id(order.comment, prefix)
                if group_id:
                    groups[group_id]['pending'].append(order)
                    groups[group_id]['total_volume'] += order.volume_initial
                    groups[group_id]['tp_count'] += 1

        # Populate treeview
        for group_id, data in groups.items():
            groups_tree.insert("", "end", values=(
                group_id,
                data['tp_count'],
                f"{data['total_volume']:.2f}",
                f"{data['total_profit']:.2f}",
                f"P:{len(data['positions'])} O:{len(data['pending'])}"
            ))

    def _refresh_individual_subtab(self, group_key, group_config):
        """Refresh individual view subtab (individual orders without grouping)"""
        widgets = self.group_widgets.get(group_key, {})
        orders_tree = widgets.get("orders_tree")

        if not orders_tree:
            return

        # Clear existing data
        for item in orders_tree.get_children():
            orders_tree.delete(item)

        prefix = group_config["prefix"]

        # Get positions starting with prefix
        positions = mt5.positions_get() if mt5.initialize() else []
        if positions:
            for pos in positions:
                if pos.comment.startswith(prefix):
                    orders_tree.insert("", "end", values=(
                        pos.symbol,
                        "BUY" if pos.type == 0 else "SELL",
                        f"{pos.volume:.2f}",
                        f"{pos.price_open:.5f}",
                        f"{pos.price_current:.5f}",
                        f"{pos.sl:.5f}" if pos.sl > 0 else "None",
                        f"{pos.tp:.5f}" if pos.tp > 0 else "None",
                        f"{pos.profit:.2f}",
                        pos.comment
                    ))

    def _extract_group_id(self, comment, prefix):
        """Extract group ID from comment based on prefix"""
        if not comment.startswith(prefix):
            return None

        # Split by underscore and get the last part as ID
        parts = comment.split('_')
        if len(parts[-1]) >= 5:  # Assuming ID is at least 5 characters
            return parts[-1]  # Return the last part as ID
        return None

    def _on_group_double_click(self, event, group_key):
        """Handle double-click on group for any group type"""
        widgets = self.group_widgets.get(group_key, {})
        groups_tree = widgets.get("groups_tree")

        if groups_tree:
            selection = groups_tree.selection()
            if selection:
                item = groups_tree.item(selection[0])
                group_id = item['values'][0]
                self._close_group(group_key, group_id)

    def _close_selected_group(self, group_key):
        """Close selected group from button for any group type"""
        widgets = self.group_widgets.get(group_key, {})
        groups_tree = widgets.get("groups_tree")

        if groups_tree:
            selection = groups_tree.selection()
            if selection:
                item = groups_tree.item(selection[0])
                group_id = item['values'][0]
                self._close_group(group_key, group_id)
            else:
                group_name = self.config.order_groups[group_key]["display_name"]
                self.util.add_status_frame(f"❌ Please select a {group_name} group to close", "yellow")

    def _close_selected_order(self, group_key):
        """Close selected order from button for any group type"""
        widgets = self.group_widgets.get(group_key, {})
        orders_tree = widgets.get("orders_tree")

        if orders_tree:
            selection = orders_tree.selection()
            if selection:
                item = orders_tree.item(selection[0])
                comment = item['values'][8]  # Comment is the 9th column (index 8)
                symbol = item['values'][0]   # Symbol is the 1st column (index 0)
                self._close_individual_order(group_key, symbol, comment)
            else:
                group_name = self.config.order_groups[group_key]["display_name"]
                self.util.add_status_frame(f"❌ Please select a {group_name} order to close", "yellow")

    def _close_group(self, group_key, group_id):
        """Close all orders (positions and pending) that belong to the given group ID"""
        group_config = self.config.order_groups[group_key]
        group_name = group_config["display_name"]
        prefix = group_config["prefix"]

        closed_positions = 0
        closed_pending = 0

        # Close positions
        positions = mt5.positions_get() if mt5.initialize() else []
        if positions:
            for pos in positions:
                if pos.comment.startswith(prefix) and pos.comment.endswith(group_id):
                    # Close position
                    price = mt5.symbol_info_tick(pos.symbol).bid if pos.type == 0 else mt5.symbol_info_tick(pos.symbol).ask
                    order_type = mt5.ORDER_TYPE_SELL if pos.type == 0 else mt5.ORDER_TYPE_BUY
                    close_request = {
                        "action": mt5.TRADE_ACTION_DEAL,
                        "position": pos.ticket,
                        "symbol": pos.symbol,
                        "volume": pos.volume,
                        "type": order_type,
                        "price": price,
                        "deviation": 10,
                        "magic": 155214,
                        "comment": f"Close {group_name} group {group_id}",
                        "type_filling": mt5.ORDER_FILLING_FOK,
                    }
                    result = mt5.order_send(close_request)
                    if result.retcode == mt5.TRADE_RETCODE_DONE:
                        closed_positions += 1

        # Close pending orders
        orders = mt5.orders_get() if mt5.initialize() else []
        if orders:
            for order in orders:
                if order.comment.startswith(prefix) and order.comment.endswith(group_id):
                    # Cancel pending order
                    cancel_request = {
                        "action": mt5.TRADE_ACTION_REMOVE,
                        "order": order.ticket,
                    }
                    result = mt5.order_send(cancel_request)
                    if result.retcode == mt5.TRADE_RETCODE_DONE:
                        closed_pending += 1

        self.util.add_status_frame(f"✅ Closed {group_name} group {group_id}: {closed_positions} positions, {closed_pending} pending orders", "green")

    def _close_individual_order(self, group_key, symbol, comment):
        """Close specific individual order by symbol and comment"""
        group_config = self.config.order_groups[group_key]
        group_name = group_config["display_name"]
        prefix = group_config["prefix"]

        closed_positions = 0

        # Close positions
        positions = mt5.positions_get() if mt5.initialize() else []
        if positions:
            for pos in positions:
                if pos.symbol == symbol and pos.comment == comment and pos.comment.startswith(prefix):
                    # Close position
                    price = mt5.symbol_info_tick(pos.symbol).bid if pos.type == 0 else mt5.symbol_info_tick(pos.symbol).ask
                    order_type = mt5.ORDER_TYPE_SELL if pos.type == 0 else mt5.ORDER_TYPE_BUY
                    close_request = {
                        "action": mt5.TRADE_ACTION_DEAL,
                        "position": pos.ticket,
                        "symbol": pos.symbol,
                        "volume": pos.volume,
                        "type": order_type,
                        "price": price,
                        "deviation": 10,
                        "magic": 155214,
                        "comment": f"Close {group_name} order {comment}",
                        "type_filling": mt5.ORDER_FILLING_FOK,
                    }
                    result = mt5.order_send(close_request)
                    if result.retcode == mt5.TRADE_RETCODE_DONE:
                        closed_positions += 1

        self.util.add_status_frame(f"✅ Closed {group_name} order {comment}: {closed_positions} positions", "green")

    def refresh_positions(self):
        """Refresh open positions data - Legacy method, now handled by dynamic system"""
        # This method is kept for backward compatibility but functionality
        # is now handled by _refresh_combined_subtab for ALL group
        pass

    def refresh_pending_orders(self):
        """Refresh pending orders data - Legacy method, now handled by dynamic system"""
        # This method is kept for backward compatibility but functionality
        # is now handled by _refresh_combined_subtab for ALL group
        pass

    # Schedule management methods for external access
    def get_schedule_status(self):
        """Get current schedule status for webhook/external access"""
        return {
            "enabled": self.schedule_enabled,
            "type": self.schedule_type,
            "running": self.schedule_running,
            "last_check": self.last_schedule_check,
            "settings": {
                "custom_minutes": self.schedule_custom_minutes,
                "daily_time": self.schedule_daily_time,
                "weekly_day": self.schedule_weekly_day,
                "weekly_time": self.schedule_weekly_time,
                "monthly_day": self.schedule_monthly_day,
                "monthly_time": self.schedule_monthly_time
            }
        }

    def set_schedule_settings(self, settings):
        """Set schedule settings from webhook/external source"""
        try:
            if "enabled" in settings:
                self.schedule_enabled = settings["enabled"]
                if self.schedule_enabled:
                    self.schedule_switch.select()
                    self.start_schedule_check()
                else:
                    self.schedule_switch.deselect()
                    self.stop_schedule_check()

            if "type" in settings:
                self.schedule_type = settings["type"]
                self.schedule_type_var.set(self.schedule_type)
                self.build_schedule_inputs()

            if "custom_minutes" in settings:
                self.schedule_custom_minutes = settings["custom_minutes"]
            if "daily_time" in settings:
                self.schedule_daily_time = settings["daily_time"]
            if "weekly_day" in settings:
                self.schedule_weekly_day = settings["weekly_day"]
            if "weekly_time" in settings:
                self.schedule_weekly_time = settings["weekly_time"]
            if "monthly_day" in settings:
                self.schedule_monthly_day = settings["monthly_day"]
            if "monthly_time" in settings:
                self.schedule_monthly_time = settings["monthly_time"]

            self.save_schedule_settings()
            self.update_schedule_status()
            return True
        except Exception as e:
            self.util.add_status_frame(f"❌ Error setting schedule: {e}", "red")
            return False

    def cleanup_schedule(self):
        """Cleanup schedule thread when application closes"""
        if self.schedule_running:
            self.stop_schedule_check()

    def cleanup_market_monitor(self):
        """Cleanup market monitor thread when application closes"""
        if self.market_monitor_running:
            self.stop_market_monitor()

    def cleanup_all_threads(self):
        """Cleanup all background threads when application closes"""
        self.cleanup_schedule()
        self.cleanup_market_monitor()
 