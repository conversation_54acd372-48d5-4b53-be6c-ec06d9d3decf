from dotenv import load_dotenv
import MetaTrader5 as mt5
import os
import json

# ===========================
# Class: Config
# ===========================
class Config:
    def __init__(self):
        load_dotenv()
        self.app_name = "My App"
        self.app_key = "myapp"

        # Debug mode setting
        self.DEBUG = os.getenv('DEBUG', 'false').lower() == 'true'

        # Log level setting (1=Critical, 2=Error, 3=Warning, 4=Info, 5=Debug)
        self.log_level = 3  # Default to Warning level
        self.log_level_names = {
            1: "Critical",
            2: "Error",
            3: "Warning",
            4: "Info",
            5: "Debug"
        }

        # Quiet mode setting (deprecated, use log_level instead)
        self.quiet_mode = False

        # AI Bot configuration (loaded from JSON file)
        self.ai_bots = {}
        self.ai_bots_file = os.path.join(os.getcwd(), "ai_bots.json")
        self.load_ai_bots()

        # AI Bot default settings for new bots
        self.ai_bot_defaults = {
            "symbol": "XU",  # Default to XAUUSD (Gold)
            "timeframes": ["H1"],  # Default timeframes (multi-timeframe support)
            "timeframe": "H1",  # Legacy single timeframe support
            "bars_back": 100,  # Default bars back
            "prompt": "Analyze the XAUUSD chart data and provide trading insights based on technical indicators.",
            "enabled": True,
            "api_provider": "gpt",  # "gpt" or "gemini"
            "auto_place_order": False,  # Auto place orders based on AI analysis
            "schedule_check": False,  # Schedule periodic checks
            "schedule_type": "custom",  # custom, hourly, daily, weekly, monthly
            "schedule_custom_minutes": 60,  # For custom schedule
            "schedule_daily_time": "09:00",  # For daily schedule
            "schedule_weekly_day": "Monday",  # For weekly schedule
            "schedule_weekly_time": "09:00",  # For weekly schedule
            "schedule_monthly_day": 1,  # For monthly schedule (1-31)
            "schedule_monthly_time": "09:00",  # For monthly schedule
            "chart_image_path": "",  # Path to chart image for analysis
            "use_chart_image": False,  # Whether to include chart image in analysis
            "use_signal_format": True,  # Use structured signal format (enabled by default)
            "multi_timeframe": True  # Enable multi-timeframe analysis
        }

        # AI API configuration
        self.ai_api_config = {
            "gpt": {
                "name": "OpenAI GPT",
                "api_key": "",  # Set via environment variable or UI
                "model": "gpt-5",  # Changed from gpt-4 to gpt-3.5-turbo for broader compatibility
                # "model": "gpt-3.5-turbo",  # Changed from gpt-4 to gpt-3.5-turbo for broader compatibility
                "max_tokens": 20000
            },
            "gemini": {
                "name": "Google Gemini",
                "api_key": "",  # Set via environment variable or UI
                "model": "gemini-1.5-flash",  # Updated to latest Gemini model
                "max_tokens": 20000
            }
        }

        # AI Signal Response Format Template
        self.ai_signal_format = {
            "template": """Signal ID: {{signal_id}}
C.GPT
Symbol: {{symbol}}
Signal: {{signal_type}}
Price: {{entry_price}}
SL: {{sl_price}}
TP1: {{tp1_price}}
TP2: {{tp2_price}}
TP3: {{tp3_price}}
TP4: {{tp4_price}}
TP5: {{tp5_price}}
Reason: {{reason}}""",

            "format_prompt": """
IMPORTANT: You will respond ONLY in this exact format, no additional text:

Signal ID: [8 random characters a-z0-9]
C.GPT
Symbol: [trading symbol]
Signal: [Buy Limit or Sell Limit]
Price: [entry price with 2 decimals]
SL: [stop loss price with 2 decimals]
TP1: [take profit 1 price with 2 decimals]
TP2: [take profit 2 price with 2 decimals]
TP3: [take profit 3 price with 2 decimals]
TP4: [take profit 4 price with 2 decimals]
TP5: [take profit 5 price with 2 decimals]
Reason: [50 words max explaining why this signal was generated]

Example:
Signal ID: a7b9c2d4
C.GPT
Symbol: XAUUSD
Signal: Buy Limit
Price: 2045.50
SL: 2040.00
TP1: 2050.00
TP2: 2055.00
TP3: 2060.00
TP4: 2065.00
TP5: 2070.00
Reason: RSI oversold at 25, MACD bullish crossover, price bounced off key support at 2040. EMA20 trending up, confluence of Fibonacci 61.8% retracement and pivot point support suggests strong buying opportunity with favorable risk-reward ratio.

Respond with ONLY this format, nothing else. but if no signal at all, respond with "NO SIGNAL" in all caps.
"""
        }

        self.account_default = "DEMO1 IUX Std"
        # self.account_default = "REAL 2 CFX"
        self.accounts = {
            "DEMO1 IUX Std":"DEMO1",
            "DEMO2 CFX Std":"DEMO2",
            "DEMO3 CFX Ult":"DEMO3",
            "DEMO4 CFX MIC":"DEMO4",
            "REAL1 XMG Std":"REAL1",
            "REAL2 CFX Std":"REAL2",
        }
        self.symbol_posfix = None
        self.symbol_default = "XU"
        self.symbols = {
            "XU":"XAUUSD", 
            "BU":"BTCUSD", 
            "U3":"US30cash", 
            "U1":"US100cash", 
            "U5":"US500cash", 
            "AU":"AUDUSD", 
            "AJ":"AUDJPY", 
            "ACD":"AUDCAD", 
            "ACF":"AUDCHF", 
            "EU":"EURUSD", 
            "GN":"GBPNZD",
            "GU":"GBPUSD",
            "UCD":"USDCAD",
            "UCF":"USDCHF",
            "UJ":"USDJPY", 
            "NCF":"NZDCHF", 
            "NJ":"NZDJPY", 
            "NU":"NZDUSD", 
        }
        self.order_type_mapping = {
            "Buy Now": mt5.ORDER_TYPE_BUY,
            "Sell Now": mt5.ORDER_TYPE_SELL,
            "Buy Limit": mt5.ORDER_TYPE_BUY_LIMIT,
            "Sell Limit": mt5.ORDER_TYPE_SELL_LIMIT,
            "Buy Stop": mt5.ORDER_TYPE_BUY_STOP,
            "Sell Stop": mt5.ORDER_TYPE_SELL_STOP,
        }
        self.action_type_mapping = {
            "Buy Now": mt5.TRADE_ACTION_DEAL,
            "Sell Now": mt5.TRADE_ACTION_DEAL,
            "Buy Limit": mt5.TRADE_ACTION_PENDING,
            "Sell Limit": mt5.TRADE_ACTION_PENDING,
            "Buy Stop": mt5.TRADE_ACTION_PENDING,
            "Sell Stop": mt5.TRADE_ACTION_PENDING,
        }
        
        self.timeframes = {
            "M5": mt5.TIMEFRAME_M5,
            "M15": mt5.TIMEFRAME_M15,
            "M30": mt5.TIMEFRAME_M30,
            "H1": mt5.TIMEFRAME_H1,
            "H4": mt5.TIMEFRAME_H4,
            # "D1": mt5.TIMEFRAME_D1
        }
        self.timeframes_start_point = {
            "M5": 1000,
            "M15": 1500,
            "M30": 2000,
            "H1": 2000,
            "H4": 3500,
            # "D1": 5000
        }
        self.timeframes_map_htf = {
            "M5": mt5.TIMEFRAME_H1,
            "M15":  mt5.TIMEFRAME_H4,
            "M30":  mt5.TIMEFRAME_H4,
            "H1":  mt5.TIMEFRAME_H8,
            "H4":  mt5.TIMEFRAME_D1,
            # "D1": 100
        }

        # ไปที่: https://notify-bot.line.me/my/
        # เลือก "Generate token" → ตั้งชื่อ → เลือกกลุ่มที่จะให้แจ้ง
        # คัดลอก Token → เอามาใส่ในโค้ดตรง LINE_TOKEN
        self.LINE_TOKEN = ''

        self.MAX_TP_FIELDS = 5
        self.MAX_ORDERS = 1

        self.RSI1_LEN = 25
        self.SMA1_LEN = 50

        self.RSI2_LEN = 50
        self.SMA2_LEN = 25

        self.RSI3_LEN = 25
        self.SMA3_LEN = 25
         
        self.BE_POINTS = 1000
        self.SL_POINTS = 1000
        self.TP1_POINTS = 2000
        self.TP2_POINTS = 3000
        self.TP3_POINTS = 4000
        self.LIMIT_LOGS = 100
        self.status_label_frame = None
        # self.timer_label = None
        self.status_label = None
        self.status_scroll_frame = None
        self.status_scroll_labels = []

        # Order groups configuration for SL and TP toggles
        # Each group has: prefix, display_name, has_group_id, default_sl_enabled, default_tp_enabled, has_subtab, subtab_type
        self.order_groups = {
            "ALL": {
                "prefix": "",
                "display_name": "All",
                "has_group_id": False,
                "default_sl_enabled": True,
                "default_tp_enabled": True,
                "has_subtab": True,
                "subtab_type": "combined",  # combined view of positions and pending orders
                "subtab_name": "All Orders"
            },
            "ZD": {
                "prefix": "ZD",
                "display_name": "ZD",
                "has_group_id": True,
                "default_sl_enabled": False,
                "default_tp_enabled": False,
                "has_subtab": True,
                "subtab_type": "grouped",  # grouped by ID
                "subtab_name": "ZD Orders"
            },
            "IN": {
                "prefix": "IN",
                "display_name": "IN",
                "has_group_id": True,
                "default_sl_enabled": False,
                "default_tp_enabled": False,
                "has_subtab": True,
                "subtab_type": "grouped",  # grouped by ID
                "subtab_name": "IN Orders"
            },
            "WH": {
                "prefix": "WH",
                "display_name": "WH",
                "has_group_id": False,
                "default_sl_enabled": False,
                "default_tp_enabled": False,
                "has_subtab": True,
                "subtab_type": "individual",  # individual orders without grouping
                "subtab_name": "WH Orders"
            },
            # "SG": {
            #     "prefix": "SG",
            #     "display_name": "Signal",
            #     "has_group_id": True,
            #     "default_sl_enabled": False,
            #     "default_tp_enabled": True,
            #     "has_subtab": True,
            #     "subtab_type": "grouped",  # grouped by ID like ZD and IN
            #     "subtab_name": "Signal Orders"
            # }
        }

    # ===========================
    # AI Bots JSON Storage Methods
    # ===========================

    def load_ai_bots(self):
        """Load AI bots from JSON file"""
        try:
            if os.path.exists(self.ai_bots_file):
                with open(self.ai_bots_file, 'r', encoding='utf-8') as f:
                    self.ai_bots = json.load(f)
                print(f"✅ Loaded {len(self.ai_bots)} AI bots from {self.ai_bots_file}")
            else:
                # Create default bot if no file exists
                self.ai_bots = {
                    "bot_1": {
                        "name": "Default XAUUSD Bot",
                        "symbol": "XU",
                        "timeframes": ["H1"],
                        "timeframe": "H1",  # Legacy support
                        "bars_back": 100,
                        "prompt": "Analyze the XAUUSD chart data and provide trading insights based on technical indicators.",
                        "enabled": True,
                        "api_provider": "gpt",
                        "auto_place_order": False,
                        "schedule_check": False,
                        "schedule_type": "custom",
                        "schedule_custom_minutes": 60,
                        "schedule_daily_time": "09:00",
                        "schedule_weekly_day": "Monday",
                        "schedule_weekly_time": "09:00",
                        "schedule_monthly_day": 1,
                        "schedule_monthly_time": "09:00",
                        "chart_image_path": "",
                        "use_chart_image": False,
                        "use_signal_format": True,  # Enabled by default
                        "multi_timeframe": False,
                        "created_at": "2025-01-01 00:00:00",
                        "last_used": None
                    }
                }
                self.save_ai_bots()
                print(f"✅ Created default AI bot and saved to {self.ai_bots_file}")
        except Exception as e:
            print(f"❌ Error loading AI bots: {e}")
            self.ai_bots = {}

    def save_ai_bots(self):
        """Save AI bots to JSON file"""
        try:
            with open(self.ai_bots_file, 'w', encoding='utf-8') as f:
                json.dump(self.ai_bots, f, indent=2, ensure_ascii=False)
            print(f"✅ Saved {len(self.ai_bots)} AI bots to {self.ai_bots_file}")
        except Exception as e:
            print(f"❌ Error saving AI bots: {e}")

    def add_ai_bot(self, bot_id, bot_config):
        """Add a new AI bot and save to file"""
        self.ai_bots[bot_id] = bot_config
        self.save_ai_bots()

    def update_ai_bot(self, bot_id, bot_config):
        """Update an existing AI bot and save to file"""
        if bot_id in self.ai_bots:
            self.ai_bots[bot_id] = bot_config
            self.save_ai_bots()

    def delete_ai_bot(self, bot_id):
        """Delete an AI bot and save to file"""
        if bot_id in self.ai_bots:
            del self.ai_bots[bot_id]
            self.save_ai_bots()
