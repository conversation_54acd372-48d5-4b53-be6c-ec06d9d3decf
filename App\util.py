import MetaTrader5 as mt5
import customtkinter as ctk
import pandas as pd
import talib as ta
import requests
import time
from datetime import datetime, time as dtime
import logging
import os
import gc
import threading
from functools import wraps
import signal
from App.debug_logger import DebugLogger
import numpy as np
import base64
import json
import random
import string

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # If python-dotenv is not installed, try to load .env manually
    env_path = os.path.join(os.getcwd(), '.env')
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
# ===========================
# Timeout and Safety Decorators
# ===========================
def timeout_handler(signum, frame):
    raise TimeoutError("Operation timed out")

def with_timeout(seconds=5):
    """Decorator to add timeout to functions"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # Set timeout signal (only works on Unix-like systems)
                if hasattr(signal, 'SIGALRM'):
                    old_handler = signal.signal(signal.SIGALRM, timeout_handler)
                    signal.alarm(seconds)

                result = func(*args, **kwargs)

                if hasattr(signal, 'SIGALRM'):
                    signal.alarm(0)  # Cancel alarm
                    signal.signal(signal.SIGALRM, old_handler)

                return result
            except TimeoutError:
                print(f"Function {func.__name__} timed out after {seconds} seconds")
                return None
            except Exception as e:
                print(f"Error in {func.__name__}: {e}")
                return None
        return wrapper
    return decorator

def safe_mt5_operation(func):
    """Decorator to safely handle MT5 operations"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            # Check MT5 connection first
            if not mt5.initialize():
                print(f"MT5 not initialized for {func.__name__}")
                return None

            result = func(*args, **kwargs)
            return result
        except Exception as e:
            print(f"MT5 operation error in {func.__name__}: {e}")
            return None
    return wrapper

# ===========================
# Class: Util
# ===========================
class Util:
    def __init__(self, config):
        self.config = config
        self._mt5_lock = threading.Lock()  # Thread safety for MT5 operations

        # Initialize debug logger
        self.debug_logger = DebugLogger(config, self)

        # Set up logging
        logging.basicConfig(
            filename=os.path.join("Logs", datetime.now().strftime('%Y-%m-%d') + '.txt'),
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(message)s',
        )


    def print_all(self ,df, option = 1): 
        if option == 1:
            # Option 1: Temporarily show all rows 
            # with pd.option_context('display.max_rows', None, 'display.max_columns', None):
            with pd.option_context('display.max_rows', None):
                print(df)
            return
        # Option 2: Permanently change display settings (for current session) 
        pd.set_option('display.max_rows', None)
        # pd.set_option('display.max_columns', None)
        print(df)
    
    def get_symbol(self, symbol_var):
        if isinstance(symbol_var, ctk.StringVar):
            symbol_var = symbol_var.get()
        if len(symbol_var) < 4:
            symbol_var = self.config.symbols[symbol_var]
        return symbol_var + self.config.symbol_posfix.get()

    def onchange_symbol(self, symbol):
        symbol =  self.config.symbols[symbol] + self.config.symbol_posfix.get()
        point = mt5.symbol_info(symbol).point
        self.set_status_label(f"⏳ Changed to {symbol} point {point} ({format(point, '.6f')})", "yellow")

        # Only print in verbose mode (not quiet mode)
        if not (hasattr(self.config, 'quiet_mode') and self.config.quiet_mode):
            print(f"⏳ Changed to {symbol} point {point} ({format(point, '.6f')})")

    def get_data(self, symbol, timeframe, bars=100):
        rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, bars)
        df = pd.DataFrame(rates)
        df['time'] = pd.to_datetime(df['time'], unit='s')
        return df

    def set_status_label(self, text, text_color = 'white'):
        self.config.status_label.configure(text=text, text_color=text_color)

    def add_status_frame(self, text, text_color='white', level=3):
        """
        Add status message with log level filtering

        Args:
            text (str): Message text
            text_color (str): Text color
            level (int): Importance level 1-5 (1=Critical, 2=Error, 3=Warning, 4=Info, 5=Debug)
        """
        # Check if message should be displayed based on log level
        current_log_level = getattr(self.config, 'log_level', 3)  # Default to level 3

        if level > current_log_level:
            return  # Skip message if level is too low

        # Check if quiet mode is enabled and modify text accordingly
        if hasattr(self.config, 'quiet_mode') and self.config.quiet_mode:
            # In quiet mode, make messages more compact
            text = self.make_compact_message(text)

        status_text = f"{time.strftime('%H:%M:%S')} : {text}"

        # Thread-safe GUI update using after() method
        def update_gui():
            try:
                # Check if the parent frame still exists and is valid
                if not hasattr(self.config, 'status_scroll_frame') or not self.config.status_scroll_frame:
                    return

                # Try to access the frame to see if it's still valid
                try:
                    self.config.status_scroll_frame.winfo_exists()
                except:
                    return  # Frame no longer exists

                label = ctk.CTkLabel(self.config.status_scroll_frame, text=status_text, anchor="w", justify="left", text_color=text_color)

                # Safely add the label
                if self.config.status_scroll_labels:
                    # Check if the first label still exists
                    try:
                        if self.config.status_scroll_labels[0].winfo_exists():
                            label.pack(fill="x", anchor="w", before=self.config.status_scroll_labels[0])
                        else:
                            # Clean up invalid references
                            self.config.status_scroll_labels = [l for l in self.config.status_scroll_labels if l.winfo_exists()]
                            label.pack(fill="x", anchor="w")
                    except:
                        label.pack(fill="x", anchor="w")
                else:
                    label.pack(fill="x", anchor="w")

                self.config.status_scroll_labels.insert(0, label)

                # Safe cleanup of old labels
                while len(self.config.status_scroll_labels) > self.config.LIMIT_LOGS:
                    try:
                        old_label = self.config.status_scroll_labels.pop()
                        if old_label.winfo_exists():
                            old_label.destroy()
                    except:
                        # Label already destroyed, just remove from list
                        pass

            except Exception as e:
                print(f"GUI update error: {e}")

        # Schedule GUI update on main thread with additional safety
        if hasattr(self.config, 'status_scroll_frame') and self.config.status_scroll_frame:
            try:
                # Check if we can safely schedule the update
                self.config.status_scroll_frame.after_idle(update_gui)
            except:
                # If scheduling fails, just log to console
                print(status_text)

        logging.info(text)

        # Print to console only if level is high enough
        if level <= current_log_level:
            print(status_text)

    def make_compact_message(self, text):
        """Make status messages more compact for quiet mode"""
        # Define patterns for common verbose messages and their compact versions
        compact_patterns = {
            # Connection messages
            r"✅ Logged in: (.+) \(TZ-(.+)\)": r"✅ Connected: \1",
            r"❌ Login failed: (.+) (.+)": r"❌ Login failed",

            # Order processing messages
            r"Processing orders for (.+) with (.+) positions and (.+) pending orders": r"📦 Processing \1",
            r"Updated (.+) positions and (.+) pending orders": r"📦 Updated \1+\2",

            # Webhook messages
            r"Webhook server started on (.+):(.+)": r"🌐 Webhook: :\2",
            r"Webhook server stopped successfully": r"🌐 Webhook stopped",

            # Auto SL/TP messages
            r"Auto SL to BE (.+) for (.+) positions": r"🎯 Auto SL: \2 pos",
            r"Auto TP (.+) for (.+) positions": r"🎯 Auto TP: \2 pos",

            # General processing messages
            r"(.+) completed successfully with (.+) changes": r"\1: \2 changes",
            r"(.+) processing completed": r"\1 done",
        }

        # Apply compact patterns
        import re
        for pattern, replacement in compact_patterns.items():
            text = re.sub(pattern, replacement, text)

        # Truncate very long messages
        if len(text) > 80:
            text = text[:77] + "..."

        return text

    def safe_mt5_call(self, operation, *args, **kwargs):
        """Thread-safe wrapper for MT5 operations with timeout"""
        with self._mt5_lock:
            try:
                # Quick connection check
                if not mt5.initialize():
                    error_code = mt5.last_error()
                    self.add_status_frame(f"❌ MT5 not initialized: {error_code}", "red", level=1)
                    return None

                # Execute the operation
                result = operation(*args, **kwargs)

                # Check if operation failed and get error details
                if result is None and hasattr(mt5, 'last_error'):
                    error_code = mt5.last_error()
                    if error_code != (0, 'Success'):
                        self.add_status_frame(f"❌ MT5 operation failed: {error_code}", "yellow")

                return result
            except Exception as e:
                self.add_status_frame(f"❌ MT5 operation exception: {e}", "red")
                return None

    def safe_order_send(self, request):
        """Thread-safe wrapper specifically for mt5.order_send"""
        with self._mt5_lock:
            try:
                # Quick connection check
                if not mt5.initialize():
                    error_code = mt5.last_error()
                    self.add_status_frame(f"❌ MT5 not initialized: {error_code}", "red")
                    return None

                # Send the order
                result = mt5.order_send(request)

                # Check if operation failed and get error details
                if result is None:
                    error_code = mt5.last_error()
                    self.add_status_frame(f"❌ Order send failed: {error_code}", "red")

                return result
            except Exception as e:
                self.add_status_frame(f"❌ Order send exception: {e}", "red")
                return None

    def cleanup_memory(self):
        """Perform memory cleanup to prevent memory leaks"""
        try:
            # Force garbage collection
            gc.collect()

            # Clean up old status labels if too many exist
            if hasattr(self.config, 'status_scroll_labels') and len(self.config.status_scroll_labels) > self.config.LIMIT_LOGS * 0.8:
                excess_count = len(self.config.status_scroll_labels) - int(self.config.LIMIT_LOGS * 0.7)
                removed_count = 0

                # Safely remove labels from the end of the list
                for _ in range(excess_count):
                    if self.config.status_scroll_labels:
                        try:
                            old_label = self.config.status_scroll_labels.pop()
                            # Check if label still exists before destroying
                            if hasattr(old_label, 'winfo_exists') and old_label.winfo_exists():
                                old_label.destroy()
                            removed_count += 1
                        except Exception as e:
                            # Label already destroyed or invalid, just continue
                            print(f"Label cleanup error: {e}")
                            continue

                if removed_count > 0:
                    # Use print instead of add_status_frame to avoid recursion
                    print(f"🧹 Memory cleanup: Removed {removed_count} old status labels")

        except Exception as e:
            print(f"Memory cleanup error: {e}")

    def is_in_restricted_time(self):
        now_utc = datetime.utcnow().time()
        return dtime(21, 0) <= now_utc < dtime(22, 0)
    
    def get_limited_positions(self, symbol, filter_comment = "", limit=10):
        positions = mt5.positions_get(symbol=symbol)
        if positions is None:
            return []
        # กรองเฉพาะ TF ที่ต้องการ
        filtered = [pos for pos in positions if pos.comment.startswith(filter_comment)]
        # คืนกลับเฉพาะจำนวนที่ต้องการ
        return filtered[:limit]
    
    def get_volatility(self, symbol, timeframe=mt5.TIMEFRAME_M15, period=14):
        rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, period + 1)
        if rates is None or len(rates) < period + 1:
            return None
        
        df = pd.DataFrame(rates)
        atr = ta.volatility.AverageTrueRange(high=df['high'], low=df['low'], close=df['close'], window=period)
        atr_value = atr.average_true_range().iloc[-1]
        return atr_value
    
    # 📤 ฟังก์ชันส่งข้อความไป LINE
    def send_line_message(self, message):
        if not self.LINE_TOKEN or self.LINE_TOKEN == '':
            return
        url = 'https://notify-api.line.me/api/notify'
        headers = {'Authorization': f'Bearer {self.config.LINE_TOKEN}'}
        data = {'message': message}
        requests.post(url, headers=headers, data=data)

    def send_telegram_message(bot_token, chat_id, message):
        # สร้าง Bot: เปิดกล่องค้นหาใน Telegram พิมพ์ @BotFather 
        # กด Start พิมพ์ /newbot → ตั้งชื่อ + username → ได้ "Bot Token"
        # เปิดแชทกับบอทของคุณ แล้วส่งข้อความใด ๆ
        # หาค่า chat_id จาก API
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        payload = {
            'chat_id': chat_id,
            'text': message,
            'parse_mode': 'HTML'
        }
        requests.post(url, data=payload)
        
    def send_discord_message(webhook_url, message):
        # ไปที่ Discord → สร้าง/เลือกห้อง → ตั้งค่า → "Integrations" → "Webhooks"
        # สร้าง Webhook และก๊อป URL มาใช้
        data = {
            "content": message
        }
        requests.post(webhook_url, json=data)

    def send_order(self, order_type, symbol, lot, entry, sl, tp, comment="Script"):
        try:
            self.add_status_frame(f"🔄 Preparing order: {order_type} {symbol} {lot} lots", "cyan")

            # Get price safely
            tick_info = self.safe_mt5_call(mt5.symbol_info_tick, symbol)
            if not tick_info:
                self.add_status_frame(f"❌ Failed to get price for {symbol}", "red")
                return None

            price = tick_info.bid if order_type == "Buy Now" else (tick_info.ask if order_type == "Sell Now" else entry)
            self.add_status_frame(f"📊 Price info: bid={tick_info.bid}, ask={tick_info.ask}, using={price}", "cyan")

            # Check if mappings exist
            if order_type not in self.config.action_type_mapping:
                self.add_status_frame(f"❌ Unknown order type: {order_type}", "red")
                return None

            if order_type not in self.config.order_type_mapping:
                self.add_status_frame(f"❌ Unknown order type mapping: {order_type}", "red")
                return None

            # Get symbol info safely
            symbol_info = self.safe_mt5_call(mt5.symbol_info, symbol)
            if not symbol_info:
                self.add_status_frame(f"❌ Failed to get point for {symbol}", "red")
                return None

            point = symbol_info.point  # e.g., 0.01 for XAUUSD
            original_sl_points = int(abs(float(entry) -  float(sl)) / point)  # e.g., 100 points

            request = {
                "action": self.config.action_type_mapping[order_type],
                "symbol": symbol,
                "volume": float(lot),
                "type": self.config.order_type_mapping[order_type],
                "price": float(price),
                "sl": float(sl),
                "tp": float(tp),
                "deviation": 10,
                "magic": original_sl_points, #161032,  # Unique identifier for your EA or script
                "comment": comment,
                "type_filling": mt5.ORDER_FILLING_FOK,
            }

            self.add_status_frame(f"📋 Order request prepared: {request}", "cyan")

            # Send the order using the safe wrapper
            self.add_status_frame(f"📤 Sending order request...", "cyan")
            result = self.safe_order_send(request)
            self.add_status_frame(f"📤 Order send result: {result}", "cyan")

        except Exception as e:
            self.add_status_frame(f"❌ Order preparation failed: {e}", "red")
            return None

        if not result:
            self.add_status_frame(f"❌ Execute failed: No return value", "yellow")
            return None
        elif result.retcode != mt5.TRADE_RETCODE_DONE:
            self.add_status_frame(f"❌ Execute failed: {result.comment}", "yellow")
            return result  # Return result even if failed, so caller can check retcode
        else:
            if order_type in ["Buy Limit", "Buy Now", "Buy Stop"]:
                self.add_status_frame(f"🟢 Sending {order_type}: {symbol} lot {lot} @ {entry}, SL {sl}, TP {tp} - {comment}", "lime")
            elif order_type in ["Sell Limit", "Sell Now", "Sell Stop"]:
                self.add_status_frame(f"🔴 Sending {order_type}: {symbol} lot {lot} @ {entry}, SL {sl}, TP {tp} - {comment}", "red")
            else:
                self.add_status_frame(f"⚠️ Execute failed: Order type unknown - {comment}", "yellow")

            return result  # Return successful result

    def positions_count(self, symbol, filter_comment = ""):
        orders = mt5.positions_get(symbol=symbol) if mt5.initialize() else [] 
        count_orders = {"All":0, "All_FT":0, "All_TF":0}
        for tf_name in self.config.timeframes:
            count_orders[tf_name] = {"B":0, "S":0}

        for order in orders:
            if order.symbol != symbol:
                continue

            count_orders["All"] += 1
            if order.comment.startswith(filter_comment) or filter_comment == "":
                count_orders["All_FT"] += 1
                for tf_name in self.config.timeframes:
                    if order.comment.startswith(filter_comment + tf_name):
                        count_orders[tf_name]["S" if order.type else "B"] += 1
                        # count_orders["All_TF"] += 1 

        return count_orders
    
    def update_SL_to_BE_by_point(self, symbol, filter_comment = "", is_moving_tp = False):
        """ Auto update orders STOP LOSS to BREAKEVEN """
        try:
            # Get positions safely with timeout
            orders = self.safe_mt5_call(mt5.positions_get, symbol=symbol)
            if not orders:
                return {"All":0, "All_FT":0, "All_TF":0}

            count_orders = {"All":0, "All_FT":0, "All_TF":0}
            for tf_name in self.config.timeframes:
                count_orders[tf_name] = {"B":0, "S":0}

            for order in orders:
                if order.symbol != symbol:
                    continue

                count_orders["All"] += 1
                if order.comment.startswith(filter_comment) or filter_comment == "":
                    count_orders["All_FT"] += 1
                    for tf_name in self.config.timeframes:
                        if order.comment.startswith(filter_comment + tf_name):
                            count_orders[tf_name]["S" if order.type else "B"] += 1
                            # count_orders["All_TF"] += 1
                    entry_price = order.price_open
                    current_price = order.price_current
                    current_tp = order.tp
                    current_sl = order.sl
                    type_ = order.type  # 0 = BUY, 1 = SELL
                    magic_number = order.magic

                    # Get symbol info safely
                    symbol_info = self.safe_mt5_call(mt5.symbol_info, order.symbol)
                    if not symbol_info:
                        continue

                    # Get original SL distance from magic number (stored as points)
                    if magic_number > 0 and magic_number != 155214:  # 155214 is default EA magic
                        # Magic number contains original SL distance in points
                        original_sl_points = magic_number
                        point_sl = original_sl_points * symbol_info.point

                        self.debug_logger.log_magic_number_info(
                            order.ticket, magic_number, original_sl_points, point_sl, order.symbol
                        )
                    else:
                        # Fallback to current calculation for orders without stored distance
                        point_sl = abs(entry_price - current_sl)
                        self.debug_logger.debug_print(f"#{order.ticket}: Using fallback SL distance calculation: {point_sl:.5f}")

                    # Validate point_sl to avoid division by zero or invalid values
                    if point_sl <= 0:
                        self.debug_logger.debug_print(f"#{order.ticket}: Invalid SL distance {point_sl}, skipping")
                        continue

                    factor = 2  # เริ่มจากคูณ 2
                    point = symbol_info.point
                    point_10 = point * 10
                    point_50 = point * 50
                    point_be = factor * point_sl

                    # point_sl = point * self.config.SL_POINTS 
                    # point_be = point * self.config.BE_POINTS 
                    new_sl = None
                    new_tp = current_tp

                    # Debug current state
                    self.debug_logger.debug_print(f"#{order.ticket}: Entry={entry_price:.5f}, Current={current_price:.5f}, SL={current_sl:.5f}, TP={current_tp:.5f}")
                    self.debug_logger.debug_print(f"#{order.ticket}: point_sl={point_sl:.5f}, point_be={point_be:.5f}, is_moving_tp={is_moving_tp}")

                    if type_ == mt5.ORDER_TYPE_BUY:  #and entry_price > current_sl:
                        if is_moving_tp:
                            # Moving TP logic for BUY orders
                            trigger_price = entry_price + (factor * point_sl)
                            self.debug_logger.debug_print(f"#{order.ticket}: BUY Moving TP - Current: {current_price:.5f}, Trigger: {trigger_price:.5f}")

                            while current_price >= entry_price + (factor * point_sl):
                                new_sl = entry_price + ((factor - 2) * point_sl + point_10)
                                new_tp = entry_price + ((factor + 2) * point_sl + point_50)
                                self.debug_logger.debug_print(f"#{order.ticket}: BUY Moving TP factor {factor} - New SL: {new_sl:.5f}, New TP: {new_tp:.5f}")
                                factor += 1
                        elif current_price >= entry_price + point_be:
                                # SL to BE logic for BUY orders
                                new_sl = entry_price + point_10
                                self.debug_logger.debug_print(f"#{order.ticket}: BUY SL to BE - Current: {current_price:.5f} >= Trigger: {entry_price + point_be:.5f}, New SL: {new_sl:.5f}")

                    elif type_ == mt5.ORDER_TYPE_SELL: # and entry_price < current_sl:
                        if is_moving_tp:
                            # Moving TP logic for SELL orders
                            trigger_price = entry_price - (factor * point_sl)
                            self.debug_logger.debug_print(f"#{order.ticket}: SELL Moving TP - Current: {current_price:.5f}, Trigger: {trigger_price:.5f}")

                            while current_price <= entry_price - (factor * point_sl):
                                new_sl = entry_price - ((factor - 2) * point_sl + point_10)
                                new_tp = entry_price - ((factor + 2) * point_sl + point_50)
                                self.debug_logger.debug_print(f"#{order.ticket}: SELL Moving TP factor {factor} - New SL: {new_sl:.5f}, New TP: {new_tp:.5f}")
                                factor += 1
                        elif current_price <= entry_price - point_be:
                                # SL to BE logic for SELL orders
                                new_sl = entry_price - point_10
                                self.debug_logger.debug_print(f"#{order.ticket}: SELL SL to BE - Current: {current_price:.5f} <= Trigger: {entry_price - point_be:.5f}, New SL: {new_sl:.5f}")

                    # Execute the order modification if needed
                    should_modify = False
                    modification_reason = []

                    # Check if SL should be modified
                    if new_sl and abs(new_sl - current_sl) > symbol_info.point:  # Only if significant change
                        should_modify = True
                        modification_reason.append(f"SL: {current_sl:.5f} -> {new_sl:.5f}")

                    # Check if TP should be modified
                    if new_tp != current_tp and abs(new_tp - current_tp) > symbol_info.point:
                        should_modify = True
                        modification_reason.append(f"TP: {current_tp:.5f} -> {new_tp:.5f}")

                    if should_modify:
                        self.debug_logger.debug_print(f"#{order.ticket}: Modifying order - {', '.join(modification_reason)}")

                        request = {"action": mt5.TRADE_ACTION_SLTP, "position": order.ticket, "sl": new_sl, "tp": new_tp}

                        # Send order modification using safe wrapper
                        result = self.safe_order_send(request)
                        if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                            # Log successful SL change
                            if new_sl and abs(new_sl - current_sl) > symbol_info.point:
                                self.debug_logger.log_order_action(order.ticket, "SL to BE", current_sl, new_sl, "SL", True)

                            # Log TP change if it was also modified
                            if new_tp != current_tp and abs(new_tp - current_tp) > symbol_info.point:
                                self.debug_logger.log_order_action(order.ticket, "Moving TP", current_tp, new_tp, "TP", True)
                        else:
                            error_msg = result.comment if result else "Unknown error"
                            self.debug_logger.log_error(Exception(f"Order send failed: {error_msg}"), "order modification", order.ticket)
                    else:
                        if new_sl:
                            self.debug_logger.debug_print(f"#{order.ticket}: No modification needed - SL change too small: {abs(new_sl - current_sl):.5f} <= {symbol_info.point:.5f}")
                        else:
                            self.debug_logger.debug_print(f"#{order.ticket}: No modification needed - conditions not met")

            # Log processing summary
            group_name = filter_comment if filter_comment else "All"
            action_type = "SL+TP" if is_moving_tp else "SL to BE"
            processed_count = sum(1 for order in orders if (order.comment.startswith(filter_comment) or filter_comment == ""))
            self.debug_logger.log_order_processing(symbol, filter_comment, count_orders["All_FT"], processed_count, action_type)

            return count_orders
        except Exception as e:
            self.debug_logger.log_error(e, "update_SL_to_BE_by_point")
            return {"All":0, "All_FT":0, "All_TF":0}

    def update_TP_to_BE_by_condition(self, symbol, condition):
        """ Auto update orders STOP LOSS to BREAKEVEN """
        orders = mt5.positions_get() if mt5.initialize() else []
        if orders is None or len(orders) == 0:
            self.add_status_frame(f"❌ Try to update TP to BE {condition}: No open positions.", "yellow")
            return 0
        count = 0
        for order in orders:
            if order.symbol != symbol:
                continue
            count+=1
            entry_price = order.price_open
            current_price = order.price_current
            current_tp = order.tp
            current_sl = order.sl
            type_ = order.type  # 0 = BUY, 1 = SELL
            profit = order.profit
            symbol_info = mt5.symbol_info(order.symbol)
            if not symbol_info:
                continue

            should_update = False
            # เงื่อนไขปิด order ตาม condition ที่รับมา
            if condition == 'all-buy' and type_ == 0 and profit < 0:
                should_update = True
            elif condition == 'all-sell' and type_ == 1 and profit < 0:
                should_update = True
            elif condition == 'all' and profit < 0:
                should_update = True

            if should_update:
                point = symbol_info.point
                point_10 = point * 10
                # point_sl = point * self.config.SL_POINTS 
                new_tp = None

                if type_ == mt5.ORDER_TYPE_BUY: 
                    new_tp = entry_price + point_10
    
                elif type_ == mt5.ORDER_TYPE_SELL:
                    new_tp = entry_price - point_10 

                if new_tp:
                    request = {"action": mt5.TRADE_ACTION_SLTP, "position": order.ticket, "sl": current_sl, "tp": new_tp} 
                    result = mt5.order_send(request)
                    if result.retcode != mt5.TRADE_RETCODE_DONE:
                        self.add_status_frame(f"❌ Execute failed: {result.comment}", "yellow") 
                    else:
                        self.add_status_frame(f"📦 Set TP to BE: position {order.ticket} TP {current_tp} >> {new_tp}")

        return count
    
    def update_SL_to_BE_by_condition(self, symbol, condition):
        """ Auto update orders STOP LOSS to BREAKEVEN """
        orders = mt5.positions_get() if mt5.initialize() else []
        if orders is None or len(orders) == 0:
            self.add_status_frame(f"❌ Try to update SL to BE {condition}: No open positions.", "yellow")
            return 0
        count = 0
        for order in orders:
            if order.symbol != symbol:
                continue
            count+=1
            entry_price = order.price_open
            current_price = order.price_current
            current_tp = order.tp
            current_sl = order.sl
            type_ = order.type  # 0 = BUY, 1 = SELL
            profit = order.profit
            symbol_info = mt5.symbol_info(order.symbol)
            if not symbol_info:
                continue

            should_update = False
            # เงื่อนไขปิด order ตาม condition ที่รับมา
            if condition == 'all-buy' and type_ == 0 and profit > 0:
                should_update = True
            elif condition == 'all-sell' and type_ == 1 and profit > 0:
                should_update = True
            elif condition == 'all' and profit > 0:
                should_update = True

            if should_update:
                point = symbol_info.point
                point_10 = point * 10
                # point_sl = point * self.config.SL_POINTS 
                new_sl = None

                if type_ == mt5.ORDER_TYPE_BUY: 
                    new_sl = entry_price + point_10
    
                elif type_ == mt5.ORDER_TYPE_SELL:
                    new_sl = entry_price - point_10 

                if new_sl:
                    request = {"action": mt5.TRADE_ACTION_SLTP, "position": order.ticket, "sl": new_sl, "tp": current_tp}
                    result = mt5.order_send(request)
                    if result.retcode != mt5.TRADE_RETCODE_DONE:
                        self.add_status_frame(f"❌ Execute failed: {result.comment}", "yellow") 
                    else:
                        self.add_status_frame(f"📦 Set SL to BE: position {order.ticket} SL {current_sl} >> {new_sl}")

        return count

    def close_orders_by_condition(self, symbol, condition, filter_comment = ""):
        orders = mt5.positions_get() if mt5.initialize() else []
        closed = 0
        if orders is None or len(orders) == 0:
            self.add_status_frame(f"❌ Try to close {condition}: No open positions.", "yellow")
            return closed
        for order in orders:
            if order.symbol != symbol:
                continue
            ticket = order.ticket
            symbol_ = order.symbol
            type_ = order.type  # 0 = BUY, 1 = SELL
            profit = order.profit
            volume = order.volume

            should_close = False

            # เงื่อนไขปิด order ตาม condition ที่รับมา
            if condition == 'buy-profit' and type_ == 0 and profit > 0:
                should_close = True
            elif condition == 'sell-profit' and type_ == 1 and profit > 0:
                should_close = True
            elif condition == 'all-profit' and profit > 0:
                should_close = True
            elif condition == 'buy-loss' and type_ == 0 and profit < 0:
                should_close = True
            elif condition == 'sell-loss' and type_ == 1 and profit < 0:
                should_close = True
            elif condition == 'all-loss' and profit < 0:
                should_close = True
            elif condition == 'all-buy' and type_ == 0:
                should_close = True
            elif condition == 'all-sell' and type_ == 1:
                should_close = True
            elif condition == 'all':
                should_close = True
            elif condition == 'filter' and order.comment.startswith(filter_comment) or filter_comment == "":
                should_close = True

            if should_close:
                closed +=1
                price = mt5.symbol_info_tick(symbol_).bid if type_ == 0 else mt5.symbol_info_tick(symbol_).ask
                order_type = mt5.ORDER_TYPE_SELL if type_ == 0 else mt5.ORDER_TYPE_BUY
                close_request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "position": ticket,
                    "symbol": symbol_,
                    "volume": volume,
                    "type": order_type,
                    "price": price,
                    "deviation": 10,
                    "magic": 155214,
                    "comment": "Auto close by condition",
                    "type_filling": mt5.ORDER_FILLING_FOK,
                }

                result = mt5.order_send(close_request)
                if result.retcode == mt5.TRADE_RETCODE_DONE:
                    self.add_status_frame(f"✅ Closed {symbol_} ({'BUY' if type_ == 0 else 'SELL'}) | profit: {profit:.2f}", "yellow")
                    # print(f"✅ Closed {symbol} ({'BUY' if type_ == 0 else 'SELL'}) | profit: {profit:.2f}")
                else:
                    self.add_status_frame(f"❌ Failed to close {symbol_}: {result.retcode}", "yellow")
                    # print(f"❌ Failed to close {symbol}: {result.retcode}")
        return closed

    def close_pending_orders_by_filter(self, symbol, filter_comment=""):
        """
        Close pending orders (Limit and Stop orders) that are not yet active
        If filter_comment is empty, closes all pending orders for the symbol
        If filter_comment is provided, only closes orders where comment ends with filter_comment
        """
        pending_orders = mt5.orders_get(symbol=symbol) if mt5.initialize() else []
        closed = 0

        if pending_orders is None or len(pending_orders) == 0:
            self.add_status_frame(f"❌ Try to close pending orders: No pending orders found for {symbol}.", "yellow")
            return closed

        for order in pending_orders:
            if order.symbol != symbol:
                continue

            # Check if we should close this order based on filter
            should_close = False
            if filter_comment == "":
                # Close all pending orders if no filter
                should_close = True
            else:
                # Close only if comment ends with filter_comment
                if order.comment.endswith(filter_comment):
                    should_close = True

            if should_close:
                # Cancel the pending order
                cancel_request = {
                    "action": mt5.TRADE_ACTION_REMOVE,
                    "order": order.ticket,
                }

                result = mt5.order_send(cancel_request)
                if result.retcode == mt5.TRADE_RETCODE_DONE:
                    closed += 1
                    order_type_name = "Buy Limit" if order.type == mt5.ORDER_TYPE_BUY_LIMIT else \
                                     "Sell Limit" if order.type == mt5.ORDER_TYPE_SELL_LIMIT else \
                                     "Buy Stop" if order.type == mt5.ORDER_TYPE_BUY_STOP else \
                                     "Sell Stop" if order.type == mt5.ORDER_TYPE_SELL_STOP else \
                                     f"Type {order.type}"
                    self.add_status_frame(f"✅ Cancelled pending order: {order.symbol} {order_type_name} | Comment: {order.comment}", "yellow")
                else:
                    self.add_status_frame(f"❌ Failed to cancel pending order {order.ticket}: {result.retcode}", "yellow")

        if closed > 0:
            filter_msg = f" with filter '{filter_comment}'" if filter_comment else ""
            self.add_status_frame(f"✅ Cancelled {closed} pending orders for {symbol}{filter_msg}", "green")

        return closed

    # ===========================
    # Chart Data and AI Analysis Functions
    # ===========================

    def get_chart_data(self, symbol, timeframe, barback):
        """Get chart data with technical indicators for AI analysis"""
        try:
            # Map timeframe string to MT5 constant
            timeframe_map = {
                "M1": mt5.TIMEFRAME_M1,
                "M5": mt5.TIMEFRAME_M5,
                "M15": mt5.TIMEFRAME_M15,
                "M30": mt5.TIMEFRAME_M30,
                "H1": mt5.TIMEFRAME_H1,
                "H4": mt5.TIMEFRAME_H4,
                "D1": mt5.TIMEFRAME_D1,
                "W1": mt5.TIMEFRAME_W1,
                "MN1": mt5.TIMEFRAME_MN1
            }

            mt5_timeframe = timeframe_map.get(timeframe.upper())
            if not mt5_timeframe:
                raise ValueError(f"Invalid timeframe: {timeframe}")

            # Get rates from MT5
            rates = mt5.copy_rates_from_pos(symbol, mt5_timeframe, 0, barback + 100)  # Get extra bars for indicators
            if rates is None or len(rates) == 0:
                raise ValueError(f"No data available for {symbol} {timeframe}")

            # Convert to numpy arrays for TA-Lib
            close = np.array([rate[4] for rate in rates], dtype=float)  # Close prices
            high = np.array([rate[2] for rate in rates], dtype=float)   # High prices
            low = np.array([rate[3] for rate in rates], dtype=float)    # Low prices
            volume = np.array([rate[5] for rate in rates], dtype=float) # Volume

            # Calculate technical indicators
            ema_20 = ta.EMA(close, timeperiod=20)
            rsi_14 = ta.RSI(close, timeperiod=14)

            # MACD calculation
            macd, macd_signal, macd_hist = ta.MACD(close, fastperiod=12, slowperiod=26, signalperiod=9)

            # Additional RSI and SMA indicators as requested
            rsi_25 = ta.RSI(close, timeperiod=25)
            sma_50_of_rsi25 = ta.SMA(rsi_25, timeperiod=50)
            rsi_50 = ta.RSI(close, timeperiod=50)
            sma_25_of_rsi50 = ta.SMA(rsi_50, timeperiod=25)

            # Format the result - take only the last 'barback' bars
            result = []
            start_idx = len(rates) - barback

            for i in range(start_idx, len(rates)):
                bar_time = datetime.fromtimestamp(rates[i][0]).strftime("%Y-%m-%d %H:%M:%S")

                result.append({
                    "time": bar_time,
                    "open": round(rates[i][1], 5),
                    "high": round(rates[i][2], 5),
                    "low": round(rates[i][3], 5),
                    "close": round(rates[i][4], 5),
                    "volume": int(rates[i][5]),
                    "ema_20": round(ema_20[i], 5) if not np.isnan(ema_20[i]) else None,
                    "rsi_14": round(rsi_14[i], 1) if not np.isnan(rsi_14[i]) else None,
                    "macd": round(macd[i], 6) if not np.isnan(macd[i]) else None,
                    "macd_signal": round(macd_signal[i], 6) if not np.isnan(macd_signal[i]) else None,
                    "rsi_25": round(rsi_25[i], 1) if not np.isnan(rsi_25[i]) else None,
                    "sma50_rsi25": round(sma_50_of_rsi25[i], 1) if not np.isnan(sma_50_of_rsi25[i]) else None,
                    "rsi_50": round(rsi_50[i], 1) if not np.isnan(rsi_50[i]) else None,
                    "sma25_rsi50": round(sma_25_of_rsi50[i], 1) if not np.isnan(sma_25_of_rsi50[i]) else None
                })

            return result

        except Exception as e:
            raise Exception(f"Chart data error: {str(e)}")

    def get_multi_timeframe_data(self, symbol, timeframes, barback):
        """Get chart data for multiple timeframes"""
        try:
            # Try different symbol variations - prioritize .iux suffix for this broker
            symbol_variations = [
                symbol + ".iux",  # Primary: IUX Markets format (XAUUSD.iux)
                symbol,  # Original symbol
                symbol + (self.config.symbol_posfix.get() if self.config.symbol_posfix else ""),  # With postfix
                symbol.replace("XAUUSD", "GOLD.iux"),  # GOLD with suffix
                symbol.replace("XAUUSD", "GOLD"),  # Common MT5 variation
                symbol.replace("XAUUSD", "XAU/USD"),  # Another variation
                symbol.replace("EURUSD", "EURUSD.iux"),  # EUR with suffix
                symbol.replace("EURUSD", "EUR/USD"),  # EUR variation
            ]

            # Remove duplicates while preserving order
            symbol_variations = list(dict.fromkeys(symbol_variations))

            successful_symbol = None
            multi_timeframe_data = {}
            total_bars = 0

            for tf in timeframes:
                chart_data = None

                # Try each symbol variation until one works
                for test_symbol in symbol_variations:
                    try:
                        self.add_status_frame(f"🔍 Trying symbol: {test_symbol}", "cyan", level=5)
                        chart_data = self.get_chart_data(test_symbol, tf, barback)
                        if chart_data:
                            successful_symbol = test_symbol
                            self.add_status_frame(f"✅ Success with symbol: {test_symbol}", "green", level=4)
                            break
                        else:
                            self.add_status_frame(f"⚠️ No data for symbol: {test_symbol}", "yellow", level=5)
                    except Exception as e:
                        self.add_status_frame(f"❌ Symbol {test_symbol} error: {str(e)}", "red", level=5)
                        continue

                if not chart_data:
                    self.add_status_frame(f"❌ Failed to get chart data for {symbol} {tf} (tried: {symbol_variations})", "red", level=2)
                    return None

                multi_timeframe_data[tf] = chart_data
                total_bars += len(chart_data)
                self.add_status_frame(f"✅ Chart data: {successful_symbol} {tf} ({len(chart_data)} bars)", "green", level=4)

            return {
                "symbol": symbol,
                "actual_symbol": successful_symbol,
                "timeframes": timeframes,
                "total_bars": total_bars,
                "data": multi_timeframe_data
            }

        except Exception as e:
            self.add_status_frame(f"❌ Multi-timeframe data error: {str(e)}", "red", level=1)
            return None

    def download_image(self, image_url):
        """Download image from URL for AI analysis"""
        try:
            response = requests.get(image_url, timeout=30)
            response.raise_for_status()

            # Convert to base64 for AI API
            image_base64 = base64.b64encode(response.content).decode('utf-8')
            return image_base64

        except Exception as e:
            self.add_status_frame(f"❌ Image download failed: {str(e)}", "red", level=2)
            return None

    def generate_signal_id(self):
        """Generate 8-character random signal ID using a-z0-9"""
        chars = string.ascii_lowercase + string.digits
        return ''.join(random.choice(chars) for _ in range(8))

    def parse_signal_format(self, analysis_text):
        """Parse structured signal format from AI response"""
        try:
            lines = analysis_text.strip().split('\n')
            signal_data = {}

            for line in lines:
                line = line.strip()
                if ':' in line:
                    key, value = line.split(':', 1)
                    key = key.strip().lower()
                    value = value.strip()

                    if 'signal id' in key:
                        signal_data['signal_id'] = value
                    elif 'symbol' in key:
                        signal_data['symbol'] = value
                    elif 'status' in key:
                        signal_data['status'] = value
                    elif 'signal' in key:
                        signal_data['signal_type'] = value
                    elif 'price' in key:
                        signal_data['entry_price'] = value
                    elif key == 'sl':
                        signal_data['sl_price'] = value
                    elif key == 'tp1':
                        signal_data['tp1_price'] = value
                    elif key == 'tp2':
                        signal_data['tp2_price'] = value
                    elif key == 'tp3':
                        signal_data['tp3_price'] = value
                    elif key == 'tp4':
                        signal_data['tp4_price'] = value
                    elif key == 'tp5':
                        signal_data['tp5_price'] = value
                    elif 'reason' in key:
                        signal_data['reason'] = value

            return signal_data

        except Exception as e:
            self.add_status_frame(f"❌ Error parsing signal format: {str(e)}", "red", level=2)
            return None

    def call_gpt_api(self, prompt, image_base64=None, use_signal_format=True):
        """Call OpenAI GPT API for analysis"""
        try:
            # Get API key from environment or config
            api_key = os.getenv('OPENAI_API_KEY') or self.config.ai_api_config.get('gpt', {}).get('api_key', '')

            # Debug: Check if API key is loaded
            if api_key:
                self.add_status_frame(f"✅ OpenAI API key found: {api_key[:10]}...{api_key[-4:]}", "green", level=4)
            else:
                self.add_status_frame("❌ OpenAI API key not found in environment or config", "red", level=2)
                # Debug: Show what's in the environment
                env_keys = [k for k in os.environ.keys() if 'OPENAI' in k.upper()]
                if env_keys:
                    self.add_status_frame(f"🔍 Found OpenAI-related env vars: {env_keys}", "yellow", level=4)
                else:
                    self.add_status_frame("🔍 No OpenAI-related environment variables found", "yellow", level=4)

            if not api_key:
                return {"error": True, "message": "OpenAI API key not configured"}

            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }

#             base_prompt = ""
#             # Add signal format requirement if requested, otherwise use bullet points
#             if use_signal_format:
#                 base_prompt += f"\n\n{self.config.ai_signal_format['format_prompt']}"
#             else:
#                 base_prompt += """

# IMPORTANT: Provide your analysis in bullet points format. Keep it short and clear.

# Format your response as:
# • Market Trend: [Brief trend analysis]
# • Key Levels: [Support/resistance levels]
# • Technical Signals: [RSI, MACD, EMA signals]
# • Trading Recommendation: [Buy/Sell/Hold with entry/exit levels]
# • Risk Assessment: [Risk level and stop loss suggestion]

# Keep each bullet point concise and actionable."""

            # Get model and determine correct parameter name first
            model = self.config.ai_api_config.get('gpt', {}).get('model', 'gpt-4')
            max_tokens_value = self.config.ai_api_config.get('gpt', {}).get('max_tokens', 1000)

            # Prepare system message with format instructions
            # Use different instructions for GPT-5 to prevent reasoning loops
            if "gpt-5" in model.lower():
                system_content = """You are a trading analyst. Give direct analysis in bullet points. NO reasoning, NO thinking, just immediate response.

START IMMEDIATELY with:
• Market Trend: [one sentence]
• Key Levels: [support/resistance numbers]
• Technical Signals: [RSI/MACD/EMA status]
• Trading Recommendation: [Buy/Sell/Hold with price]
• Risk Assessment: [risk level and stop loss]

RESPOND NOW."""
            else:
                system_content = """You are an expert trading analyst. Provide immediate, direct analysis without extensive reasoning.

RESPOND IMMEDIATELY with this exact format:
• Market Trend: [Brief trend analysis]
• Key Levels: [Support/resistance levels]
• Technical Signals: [RSI, MACD, EMA signals]
• Trading Recommendation: [Buy/Sell/Hold with entry/exit levels]
• Risk Assessment: [Risk level and stop loss suggestion]

Be concise and direct. Start your response immediately with the bullet points."""

            # Add signal format if requested
            if use_signal_format:
                system_content += f"\n\n{self.config.ai_signal_format['format_prompt']}"

            # Prepare messages
            messages = [
                {
                    "role": "system",
                    "content": system_content
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ]

            # Add image if provided (GPT-4 Vision)
            if image_base64:
                messages[-1]["content"] = [
                    {"type": "text", "text": prompt},
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{image_base64}"
                        }
                    }
                ]

            # Increase max tokens for GPT-5 due to reasoning tokens
            if "gpt-5" in model.lower():
                # GPT-5 needs more tokens due to reasoning process
                # For large prompts, increase even more
                prompt_length = len(prompt)
                if prompt_length > 2000:
                    max_tokens_value = max(max_tokens_value, 20000)  # Large prompts need more tokens
                    self.add_status_frame("📊 Large prompt detected, increasing GPT-5 token limit to 6000", "cyan", level=4)
                else:
                    max_tokens_value = max(max_tokens_value, 4000)  # Minimum 4000 for GPT-5

            payload = {
                "model": model,
                "messages": messages
            }

            # Use correct parameters based on model
            if "gpt-5" in model.lower():
                payload["max_completion_tokens"] = max_tokens_value
                # GPT-5 only supports temperature = 1 (default), so we don't set it
            else:
                payload["max_tokens"] = max_tokens_value
                payload["temperature"] = 0.7  # Only for older models

            response = requests.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=payload,
                timeout=60
            )
            print(response.json())

            if response.status_code == 200:
                result = response.json()
                analysis = result['choices'][0]['message']['content']
                finish_reason = result['choices'][0].get('finish_reason', 'unknown')

                # Check for empty content
                if not analysis or analysis.strip() == '':
                    if finish_reason == 'length':
                        # If GPT-5 failed due to reasoning tokens, try GPT-4 as fallback
                        if "gpt-5" in model.lower():
                            self.add_status_frame("⚠️ GPT-5 failed due to reasoning tokens, trying GPT-4 fallback...", "yellow", level=3)

                            # Retry with GPT-4
                            fallback_payload = payload.copy()
                            fallback_payload["model"] = "gpt-4"
                            fallback_payload["max_tokens"] = 1500  # Use max_tokens for GPT-4
                            if "max_completion_tokens" in fallback_payload:
                                del fallback_payload["max_completion_tokens"]
                            fallback_payload["temperature"] = 0.7

                            fallback_response = requests.post(
                                "https://api.openai.com/v1/chat/completions",
                                headers=headers,
                                json=fallback_payload,
                                timeout=60
                            )

                            if fallback_response.status_code == 200:
                                fallback_result = fallback_response.json()
                                fallback_analysis = fallback_result['choices'][0]['message']['content']
                                if fallback_analysis and fallback_analysis.strip():
                                    self.add_status_frame("✅ GPT-4 fallback successful", "green", level=4)
                                    return {"error": False, "analysis": fallback_analysis}

                        error_msg = f"GPT response was truncated due to token limit. Used {result.get('usage', {}).get('total_tokens', 'unknown')} tokens. Try increasing max_tokens or shortening the prompt."
                        self.add_status_frame(f"❌ {error_msg}", "red", level=2)
                        return {"error": True, "message": error_msg}
                    else:
                        error_msg = f"GPT returned empty content (finish_reason: {finish_reason})"
                        self.add_status_frame(f"❌ {error_msg}", "red", level=2)
                        return {"error": True, "message": error_msg}

                self.add_status_frame("✅ GPT analysis completed", "green", level=4)
                return {"error": False, "analysis": analysis}
            elif response.status_code == 401:
                error_msg = "OpenAI API authentication failed - invalid API key"
                self.add_status_frame(f"❌ {error_msg}", "red", level=2)
                return {"error": True, "message": error_msg}
            elif response.status_code == 429:
                try:
                    error_data = response.json()
                    if error_data.get("error", {}).get("code") == "insufficient_quota":
                        error_msg = "OpenAI API quota exceeded - please check your billing and usage limits at https://platform.openai.com/usage"
                    else:
                        error_msg = "OpenAI API rate limit exceeded - please wait and try again"
                except:
                    error_msg = "OpenAI API rate limit or quota exceeded"
                self.add_status_frame(f"❌ {error_msg}", "red", level=2)
                return {"error": True, "message": error_msg}
            elif response.status_code == 404:
                try:
                    error_data = response.json()
                    if "model" in error_data.get("error", {}).get("message", "").lower():
                        error_msg = f"OpenAI model '{payload.get('model', 'unknown')}' not available - try gpt-3.5-turbo instead"
                    else:
                        error_msg = "OpenAI API endpoint not found"
                except:
                    error_msg = "OpenAI API endpoint not found"
                self.add_status_frame(f"❌ {error_msg}", "red", level=2)
                return {"error": True, "message": error_msg}
            else:
                error_msg = f"GPT API error: {response.status_code} - {response.text}"
                self.add_status_frame(f"❌ {error_msg}", "red", level=2)
                return {"error": True, "message": error_msg}

        except Exception as e:
            error_msg = f"GPT API call failed: {str(e)}"
            self.add_status_frame(f"❌ {error_msg}", "red", level=2)
            return {"error": True, "message": error_msg}

    def call_gemini_api(self, prompt, image_base64=None, use_signal_format=True):
        """Call Google Gemini API for analysis"""
        try:
            # Get API key from environment or config
            api_key = os.getenv('GEMINI_API_KEY') or self.config.ai_api_config.get('gemini', {}).get('api_key', '')

            # Debug: Check if API key is loaded
            if api_key:
                self.add_status_frame(f"✅ Gemini API key found: {api_key[:10]}...{api_key[-4:]}", "green", level=4)
            else:
                self.add_status_frame("❌ Gemini API key not found in environment or config", "red", level=2)
                return {"error": True, "message": "Gemini API key not configured"}

            # Prepare the request URL - use latest Gemini model
            model_name = "gemini-1.5-flash" if not image_base64 else "gemini-1.5-pro"
            url = f"https://generativelanguage.googleapis.com/v1beta/models/{model_name}:generateContent?key={api_key}"

            # Prepare system instruction
            system_instruction = """You are an expert trading analyst. Analyze the provided market data and give actionable trading insights.

IMPORTANT: Always provide your analysis in bullet points format. Keep it short and clear.

Format your response as:
• Market Trend: [Brief trend analysis]
• Key Levels: [Support/resistance levels]
• Technical Signals: [RSI, MACD, EMA signals]
• Trading Recommendation: [Buy/Sell/Hold with entry/exit levels]
• Risk Assessment: [Risk level and stop loss suggestion]

Keep each bullet point concise and actionable. Always provide a complete response."""

            # Add signal format if requested
            if use_signal_format:
                system_instruction += f"\n\n{self.config.ai_signal_format['format_prompt']}"

            # Prepare content parts
            parts = [{"text": prompt}]

            # Add image if provided
            if image_base64:
                parts.append({
                    "inline_data": {
                        "mime_type": "image/jpeg",
                        "data": image_base64
                    }
                })

            payload = {
                "system_instruction": {
                    "parts": [{"text": system_instruction}]
                },
                "contents": [{
                    "parts": parts
                }],
                "generationConfig": {
                    "maxOutputTokens": self.config.ai_api_config.get('gemini', {}).get('max_tokens', 1000),
                    "temperature": 0.7,
                    "topP": 0.8,
                    "topK": 10
                },
                "safetySettings": [
                    {
                        "category": "HARM_CATEGORY_HARASSMENT",
                        "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                    },
                    {
                        "category": "HARM_CATEGORY_HATE_SPEECH",
                        "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                    },
                    {
                        "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                        "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                    },
                    {
                        "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                        "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                    }
                ]
            }

            headers = {
                "Content-Type": "application/json"
            }

            response = requests.post(url, headers=headers, json=payload, timeout=60)

            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    candidate = result['candidates'][0]
                    if 'content' in candidate and 'parts' in candidate['content']:
                        analysis = candidate['content']['parts'][0]['text']
                        self.add_status_frame("✅ Gemini analysis completed", "green", level=4)
                        return {"error": False, "analysis": analysis}
                    else:
                        # Handle blocked content or other issues
                        finish_reason = candidate.get('finishReason', 'UNKNOWN')
                        if finish_reason == 'SAFETY':
                            return {"error": True, "message": "Gemini blocked content due to safety filters"}
                        else:
                            return {"error": True, "message": f"Gemini response incomplete: {finish_reason}"}
                else:
                    return {"error": True, "message": "No analysis generated by Gemini"}
            elif response.status_code == 400:
                try:
                    error_data = response.json()
                    error_msg = error_data.get("error", {}).get("message", "Bad request")
                    self.add_status_frame(f"❌ Gemini API error: {error_msg}", "red", level=2)
                    return {"error": True, "message": f"Gemini API error: {error_msg}"}
                except:
                    error_msg = f"Gemini API bad request: {response.text}"
                    self.add_status_frame(f"❌ {error_msg}", "red", level=2)
                    return {"error": True, "message": error_msg}
            elif response.status_code == 403:
                error_msg = "Gemini API authentication failed - invalid API key or quota exceeded"
                self.add_status_frame(f"❌ {error_msg}", "red", level=2)
                return {"error": True, "message": error_msg}
            else:
                error_msg = f"Gemini API error: {response.status_code} - {response.text}"
                self.add_status_frame(f"❌ {error_msg}", "red", level=2)
                return {"error": True, "message": error_msg}

        except Exception as e:
            error_msg = f"Gemini API call failed: {str(e)}"
            self.add_status_frame(f"❌ {error_msg}", "red", level=2)
            return {"error": True, "message": error_msg}

    def generate_prompt(self, chart_data_result, custom_prompt, symbol, timeframes, use_signal_format=True):
        """Generate prompt for AI analysis"""
        try:
            # Prepare the analysis prompt
            base_prompt = f"""
Analyze the {symbol} trading data for timeframes {timeframes}.

Technical Analysis Data:
- Total bars analyzed: {chart_data_result['total_bars']}
- Timeframes: {', '.join(timeframes)}
- Use Trend Line, Fibonacci Retracement, Support/resistance, Supply/demand and Pivot Points in your analysis.

Chart Data Summary:
{chart_data_result['data']}

"""

            # Add chart data summary for each timeframe
#             for tf, data in chart_data_result['data'].items():
#                 if data:
#                     latest_bar = data[-1]
#                     base_prompt += f"""
# {tf} Timeframe (Latest Bar):
# - Price: O:{latest_bar['open']} H:{latest_bar['high']} L:{latest_bar['low']} C:{latest_bar['close']}
# - EMA20: {latest_bar['ema_20']}
# - RSI14: {latest_bar['rsi_14']}
# - MACD: {latest_bar['macd']} Signal: {latest_bar['macd_signal']}
# - RSI25: {latest_bar['rsi_25']} SMA50: {latest_bar['sma50_rsi25']}
# - RSI50: {latest_bar['rsi_50']} SMA25: {latest_bar['sma25_rsi50']}
# """

            # Add custom prompt if provided
            if custom_prompt:
                base_prompt += f"\n\nAdditional Instructions: {custom_prompt}"

            # Format instructions are now in system message, so we don't add them here
            # This keeps the user prompt focused on the data and custom instructions

            return base_prompt

        except Exception as e:
            error_msg = f"AI analysis error: {str(e)}"
            self.add_status_frame(f"❌ {error_msg}", "red", level=1)
            return None

    def perform_ai_analysis(self, chart_data_result, custom_prompt, ai_provider, image_url, symbol, timeframes, use_signal_format=True):
        """Perform AI analysis using specified provider"""
        base_prompt = self.generate_prompt(chart_data_result, custom_prompt, symbol, timeframes, use_signal_format)
        try: 
            # Download image if provided
            image_base64 = None
            if image_url:
                image_base64 = self.download_image(image_url)
                if image_base64:
                    base_prompt += "\n\nA chart image has been provided for visual analysis."

            # Call appropriate AI API
            if ai_provider == "gpt":
                analysis_result = self.call_gpt_api(base_prompt, image_base64, use_signal_format)
            elif ai_provider == "gemini":
                analysis_result = self.call_gemini_api(base_prompt, image_base64, use_signal_format)
            else:
                return {"error": True, "message": f"Unsupported AI provider: {ai_provider}"}

            if analysis_result.get("error"):
                return analysis_result

            # Parse signal format if requested
            signal_data = None
            if use_signal_format:
                signal_data = self.parse_signal_format(analysis_result["analysis"])

            # Prepare final response
            response = {
                "error": False,
                "message": "AI analysis completed successfully",
                "symbol": symbol,
                "timeframes": timeframes,
                "ai_provider": ai_provider.upper(),
                "analysis": analysis_result["analysis"],
                # "chart_data": chart_data_result,
                # "custom_prompt": custom_prompt,
                "prompt": base_prompt,
                "image_analyzed": bool(image_base64),
                "use_signal_format": use_signal_format,
                "timestamp": datetime.now().isoformat()
            }

            # Add parsed signal data if available
            if signal_data:
                response["signal_data"] = signal_data
                response["structured_signal"] = True

            return response

        except Exception as e:
            error_msg = f"AI analysis error: {str(e)}"
            self.add_status_frame(f"❌ {error_msg}", "red", level=1)
            return {"error": True, "message": error_msg}

    def save_signal_log(self, analysis_result, bot_name="Unknown"):
        """Save AI analysis result to Logs/Signals folder"""
        try:
            # Create Logs/Signals directory if it doesn't exist
            logs_dir = os.path.join(os.getcwd(), "Logs", "Signals")
            os.makedirs(logs_dir, exist_ok=True)

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{bot_name}_{timestamp}.json"
            filepath = os.path.join(logs_dir, filename)

            # Save analysis result as JSON
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(analysis_result, f, indent=2, ensure_ascii=False)

            self.add_status_frame(f"📁 Signal saved: {filename}", "cyan", level=4)
            return filepath

        except Exception as e:
            self.add_status_frame(f"❌ Failed to save signal log: {str(e)}", "red", level=2)
            return None