{"error": false, "message": "AI analysis completed successfully", "symbol": "XAUUSD.iux", "timeframes": ["H1"], "ai_provider": "GPT", "analysis": "", "prompt": "\nAnalyze the XAUUSD.iux trading data for timeframes ['H1'].\n\nTechnical Analysis Data:\n- Total bars analyzed: 100\n- Timeframes: H1\n- Use Trend Line, Fibonacci Retracement, Support/resistance, Supply/demand and Pivot Points in your analysis.\n\nChart Data Summary: {'H1': [{'time': '2025-08-25 21:00:00', 'open': np.float64(3364.53), 'high': np.float64(3371.31), 'low': np.float64(3362.15), 'close': np.float64(3370.87), 'volume': 12737, 'ema_20': np.float64(3363.98377), 'rsi_14': np.float64(59.7), 'macd': np.float64(4.904202), 'macd_signal': np.float64(5.775794), 'rsi_25': np.float64(59.1), 'sma50_rsi25': np.float64(53.6), 'rsi_50': np.float64(57.2), 'sma25_rsi50': np.float64(57.1)}, {'time': '2025-08-25 22:00:00', 'open': np.float64(3370.83), 'high': np.float64(3375.42), 'low': np.float64(3369.31), 'close': np.float64(3374.39), 'volume': 14276, 'ema_20': np.float64(3364.97484), 'rsi_14': np.float64(62.4), 'macd': np.float64(5.159004), 'macd_signal': np.float64(5.652436), 'rsi_25': np.float64(60.7), 'sma50_rsi25': np.float64(54.0), 'rsi_50': np.float64(58.1), 'sma25_rsi50': np.float64(57.6)}, {'time': '2025-08-25 23:00:00', 'open': np.float64(3374.38), 'high': np.float64(3376.27), 'low': np.float64(3371.92), 'close': np.float64(3374.06), 'volume': 8856, 'ema_20': np.float64(3365.8401), 'rsi_14': np.float64(62.0), 'macd': np.float64(5.273518), 'macd_signal': np.float64(5.576653), 'rsi_25': np.float64(60.5), 'sma50_rsi25': np.float64(54.2), 'rsi_50': np.float64(58.0), 'sma25_rsi50': np.float64(57.9)}, {'time': '2025-08-26 00:00:00', 'open': np.float64(3374.05), 'high': np.float64(3374.36), 'low': np.float64(3371.73), 'close': np.float64(3373.87), 'volume': 6073, 'ema_20': np.float64(3366.60485), 'rsi_14': np.float64(61.8), 'macd': np.float64(5.287984), 'macd_signal': np.float64(5.518919), 'rsi_25': np.float64(60.3), 'sma50_rsi25': np.float64(54.4), 'rsi_50': np.float64(57.9), 'sma25_rsi50': np.float64(57.8)}, {'time': '2025-08-26 01:00:00', 'open': np.float64(3373.89), 'high': np.float64(3374.88), 'low': np.float64(3370.89), 'close': np.float64(3371.1), 'volume': 6045, 'ema_20': np.float64(3367.03296), 'rsi_14': np.float64(58.0), 'macd': np.float64(5.018087), 'macd_signal': np.float64(5.418752), 'rsi_25': np.float64(58.3), 'sma50_rsi25': np.float64(54.6), 'rsi_50': np.float64(56.9), 'sma25_rsi50': np.float64(57.7)}, {'time': '2025-08-26 02:00:00', 'open': np.float64(3371.08), 'high': np.float64(3373.28), 'low': np.float64(3369.82), 'close': np.float64(3370.49), 'volume': 5083, 'ema_20': np.float64(3367.3622), 'rsi_14': np.float64(57.1), 'macd': np.float64(4.700781), 'macd_signal': np.float64(5.275158), 'rsi_25': np.float64(57.9), 'sma50_rsi25': np.float64(54.6), 'rsi_50': np.float64(56.7), 'sma25_rsi50': np.float64(57.6)}, {'time': '2025-08-26 03:00:00', 'open': np.float64(3370.6), 'high': np.float64(3370.89), 'low': np.float64(3365.2), 'close': np.float64(3365.42), 'volume': 7252, 'ema_20': np.float64(3367.17723), 'rsi_14': np.float64(50.6), 'macd': np.float64(3.994166), 'macd_signal': np.float64(5.01896), 'rsi_25': np.float64(54.3), 'sma50_rsi25': np.float64(54.7), 'rsi_50': np.float64(54.9), 'sma25_rsi50': np.float64(57.4)}, {'time': '2025-08-26 04:00:00', 'open': np.float64(3365.41), 'high': np.float64(3366.15), 'low': np.float64(3364.26), 'close': np.float64(3365.53), 'volume': 3260, 'ema_20': np.float64(3367.02035), 'rsi_14': np.float64(50.8), 'macd': np.float64(3.403807), 'macd_signal': np.float64(4.695929), 'rsi_25': np.float64(54.4), 'sma50_rsi25': np.float64(54.8), 'rsi_50': np.float64(54.9), 'sma25_rsi50': np.float64(57.2)}, {'time': '2025-08-26 06:00:00', 'open': np.float64(3366.51), 'high': np.float64(3367.13), 'low': np.float64(3363.76), 'close': np.float64(3364.2), 'volume': 2046, 'ema_20': np.float64(3366.75174), 'rsi_14': np.float64(49.1), 'macd': np.float64(2.796389), 'macd_signal': np.float64(4.316021), 'rsi_25': np.float64(53.4), 'sma50_rsi25': np.float64(54.8), 'rsi_50': np.float64(54.4), 'sma25_rsi50': np.float64(56.9)}, {'time': '2025-08-26 07:00:00', 'open': np.float64(3364.21), 'high': np.float64(3364.21), 'low': np.float64(3351.7), 'close': np.float64(3354.89), 'volume': 6625, 'ema_20': np.float64(3365.62205), 'rsi_14': np.float64(39.2), 'macd': np.float64(1.545946), 'macd_signal': np.float64(3.762006), 'rsi_25': np.float64(47.5), 'sma50_rsi25': np.float64(54.8), 'rsi_50': np.float64(51.3), 'sma25_rsi50': np.float64(56.6)}, {'time': '2025-08-26 08:00:00', 'open': np.float64(3354.88), 'high': np.float64(3386.42), 'low': np.float64(3351.26), 'close': np.float64(3383.26), 'volume': 25881, 'ema_20': np.float64(3367.30186), 'rsi_14': np.float64(63.4), 'macd': np.float64(2.81177), 'macd_signal': np.float64(3.571959), 'rsi_25': np.float64(61.2), 'sma50_rsi25': np.float64(55.0), 'rsi_50': np.float64(58.7), 'sma25_rsi50': np.float64(56.6)}, {'time': '2025-08-26 09:00:00', 'open': np.float64(3383.27), 'high': np.float64(3385.09), 'low': np.float64(3371.93), 'close': np.float64(3376.82), 'volume': 16554, 'ema_20': np.float64(3368.20835), 'rsi_14': np.float64(57.8), 'macd': np.float64(3.257737), 'macd_signal': np.float64(3.509114), 'rsi_25': np.float64(57.6), 'sma50_rsi25': np.float64(55.2), 'rsi_50': np.float64(56.7), 'sma25_rsi50': np.float64(56.6)}, {'time': '2025-08-26 10:00:00', 'open': np.float64(3376.71), 'high': np.float64(3379.04), 'low': np.float64(3372.89), 'close': np.float64(3375.38), 'volume': 8786, 'ema_20': np.float64(3368.89136), 'rsi_14': np.float64(56.6), 'macd': np.float64(3.455145), 'macd_signal': np.float64(3.498321), 'rsi_25': np.float64(56.9), 'sma50_rsi25': np.float64(55.3), 'rsi_50': np.float64(56.3), 'sma25_rsi50': np.float64(56.5)}, {'time': '2025-08-26 11:00:00', 'open': np.float64(3375.27), 'high': np.float64(3375.45), 'low': np.float64(3369.86), 'close': np.float64(3372.22), 'volume': 7370, 'ema_20': np.float64(3369.20838), 'rsi_14': np.float64(53.9), 'macd': np.float64(3.318354), 'macd_signal': np.float64(3.462327), 'rsi_25': np.float64(55.2), 'sma50_rsi25': np.float64(55.4), 'rsi_50': np.float64(55.3), 'sma25_rsi50': np.float64(56.5)}, {'time': '2025-08-26 12:00:00', 'open': np.float64(3372.2), 'high': np.float64(3378.96), 'low': np.float64(3372.09), 'close': np.float64(3377.34), 'volume': 5885, 'ema_20': np.float64(3369.98282), 'rsi_14': np.float64(57.4), 'macd': np.float64(3.581799), 'macd_signal': np.float64(3.486222), 'rsi_25': np.float64(57.3), 'sma50_rsi25': np.float64(55.6), 'rsi_50': np.float64(56.5), 'sma25_rsi50': np.float64(56.5)}, {'time': '2025-08-26 13:00:00', 'open': np.float64(3377.38), 'high': np.float64(3379.04), 'low': np.float64(3372.44), 'close': np.float64(3376.7), 'volume': 7271, 'ema_20': np.float64(3370.62255), 'rsi_14': np.float64(56.8), 'macd': np.float64(3.69633), 'macd_signal': np.float64(3.528243), 'rsi_25': np.float64(57.0), 'sma50_rsi25': np.float64(55.8), 'rsi_50': np.float64(56.3), 'sma25_rsi50': np.float64(56.4)}, {'time': '2025-08-26 14:00:00', 'open': np.float64(3376.68), 'high': np.float64(3377.67), 'low': np.float64(3372.46), 'close': np.float64(3376.18), 'volume': 8313, 'ema_20': np.float64(3371.15183), 'rsi_14': np.float64(56.3), 'macd': np.float64(3.702457), 'macd_signal': np.float64(3.563086), 'rsi_25': np.float64(56.7), 'sma50_rsi25': np.float64(56.0), 'rsi_50': np.float64(56.2), 'sma25_rsi50': np.float64(56.4)}, {'time': '2025-08-26 15:00:00', 'open': np.float64(3376.17), 'high': np.float64(3379.09), 'low': np.float64(3367.24), 'close': np.float64(3376.68), 'volume': 12730, 'ema_20': np.float64(3371.67832), 'rsi_14': np.float64(56.7), 'macd': np.float64(3.70495), 'macd_signal': np.float64(3.591459), 'rsi_25': np.float64(56.9), 'sma50_rsi25': np.float64(56.2), 'rsi_50': np.float64(56.3), 'sma25_rsi50': np.float64(56.4)}, {'time': '2025-08-26 16:00:00', 'open': np.float64(3376.66), 'high': np.float64(3379.8), 'low': np.float64(3375.5), 'close': np.float64(3376.69), 'volume': 8677, 'ema_20': np.float64(3372.15562), 'rsi_14': np.float64(56.7), 'macd': np.float64(3.665479), 'macd_signal': np.float64(3.606263), 'rsi_25': np.float64(56.9), 'sma50_rsi25': np.float64(56.5), 'rsi_50': np.float64(56.3), 'sma25_rsi50': np.float64(56.3)}, {'time': '2025-08-26 17:00:00', 'open': np.float64(3376.68), 'high': np.float64(3378.59), 'low': np.float64(3369.74), 'close': np.float64(3372.02), 'volume': 9546, 'ema_20': np.float64(3372.14271), 'rsi_14': np.float64(51.7), 'macd': np.float64(3.220247), 'macd_signal': np.float64(3.52906), 'rsi_25': np.float64(54.1), 'sma50_rsi25': np.float64(56.7), 'rsi_50': np.float64(54.8), 'sma25_rsi50': np.float64(56.2)}, {'time': '2025-08-26 18:00:00', 'open': np.float64(3372.03), 'high': np.float64(3375.14), 'low': np.float64(3367.22), 'close': np.float64(3372.05), 'volume': 7106, 'ema_20': np.float64(3372.13388), 'rsi_14': np.float64(51.7), 'macd': np.float64(2.837114), 'macd_signal': np.float64(3.390671), 'rsi_25': np.float64(54.1), 'sma50_rsi25': np.float64(56.9), 'rsi_50': np.float64(54.8), 'sma25_rsi50': np.float64(56.2)}, {'time': '2025-08-26 19:00:00', 'open': np.float64(3372.12), 'high': np.float64(3380.28), 'low': np.float64(3370.72), 'close': np.float64(3377.46), 'volume': 10467, 'ema_20': np.float64(3372.64113), 'rsi_14': np.float64(56.9), 'macd': np.float64(2.936174), 'macd_signal': np.float64(3.299771), 'rsi_25': np.float64(56.8), 'sma50_rsi25': np.float64(57.2), 'rsi_50': np.float64(56.2), 'sma25_rsi50': np.float64(56.2)}, {'time': '2025-08-26 20:00:00', 'open': np.float64(3377.45), 'high': np.float64(3379.82), 'low': np.float64(3370.95), 'close': np.float64(3371.97), 'volume': 11917, 'ema_20': np.float64(3372.57721), 'rsi_14': np.float64(50.9), 'macd': np.float64(2.542375), 'macd_signal': np.float64(3.148292), 'rsi_25': np.float64(53.4), 'sma50_rsi25': np.float64(57.4), 'rsi_50': np.float64(54.5), 'sma25_rsi50': np.float64(56.1)}, {'time': '2025-08-26 21:00:00', 'open': np.float64(3371.98), 'high': np.float64(3378.17), 'low': np.float64(3370.55), 'close': np.float64(3375.61), 'volume': 18383, 'ema_20': np.float64(3372.86605), 'rsi_14': np.float64(54.3), 'macd': np.float64(2.495241), 'macd_signal': np.float64(3.017682), 'rsi_25': np.float64(55.3), 'sma50_rsi25': np.float64(57.7), 'rsi_50': np.float64(55.4), 'sma25_rsi50': np.float64(56.0)}, {'time': '2025-08-26 22:00:00', 'open': np.float64(3375.73), 'high': np.float64(3379.57), 'low': np.float64(3370.34), 'close': np.float64(3379.34), 'volume': 15886, 'ema_20': np.float64(3373.48262), 'rsi_14': np.float64(57.6), 'macd': np.float64(2.727427), 'macd_signal': np.float64(2.959631), 'rsi_25': np.float64(57.0), 'sma50_rsi25': np.float64(57.9), 'rsi_50': np.float64(56.4), 'sma25_rsi50': np.float64(56.0)}, {'time': '2025-08-26 23:00:00', 'open': np.float64(3379.35), 'high': np.float64(3389.39), 'low': np.float64(3378.68), 'close': np.float64(3386.72), 'volume': 12852, 'ema_20': np.float64(3374.74332), 'rsi_14': np.float64(63.1), 'macd': np.float64(3.466975), 'macd_signal': np.float64(3.0611), 'rsi_25': np.float64(60.3), 'sma50_rsi25': np.float64(58.2), 'rsi_50': np.float64(58.1), 'sma25_rsi50': np.float64(56.1)}, {'time': '2025-08-27 00:00:00', 'open': np.float64(3386.74), 'high': np.float64(3387.69), 'low': np.float64(3382.64), 'close': np.float64(3383.12), 'volume': 8800, 'ema_20': np.float64(3375.5411), 'rsi_14': np.float64(59.1), 'macd': np.float64(3.719703), 'macd_signal': np.float64(3.19282), 'rsi_25': np.float64(58.1), 'sma50_rsi25': np.float64(58.6), 'rsi_50': np.float64(57.0), 'sma25_rsi50': np.float64(56.0)}, {'time': '2025-08-27 01:00:00', 'open': np.float64(3383.14), 'high': np.float64(3385.93), 'low': np.float64(3381.56), 'close': np.float64(3381.78), 'volume': 6951, 'ema_20': np.float64(3376.13528), 'rsi_14': np.float64(57.6), 'macd': np.float64(3.768426), 'macd_signal': np.float64(3.307941), 'rsi_25': np.float64(57.2), 'sma50_rsi25': np.float64(58.7), 'rsi_50': np.float64(56.6), 'sma25_rsi50': np.float64(56.0)}, {'time': '2025-08-27 02:00:00', 'open': np.float64(3381.79), 'high': np.float64(3385.71), 'low': np.float64(3380.31), 'close': np.float64(3385.64), 'volume': 5955, 'ema_20': np.float64(3377.04049), 'rsi_14': np.float64(60.7), 'macd': np.float64(4.071574), 'macd_signal': np.float64(3.460668), 'rsi_25': np.float64(59.0), 'sma50_rsi25': np.float64(58.6), 'rsi_50': np.float64(57.5), 'sma25_rsi50': np.float64(56.0)}, {'time': '2025-08-27 03:00:00', 'open': np.float64(3385.65), 'high': np.float64(3390.94), 'low': np.float64(3385.22), 'close': np.float64(3389.7), 'volume': 8398, 'ema_20': np.float64(3378.24616), 'rsi_14': np.float64(63.6), 'macd': np.float64(4.586558), 'macd_signal': np.float64(3.685846), 'rsi_25': np.float64(60.8), 'sma50_rsi25': np.float64(58.5), 'rsi_50': np.float64(58.5), 'sma25_rsi50': np.float64(56.0)}, {'time': '2025-08-27 04:00:00', 'open': np.float64(3389.72), 'high': np.float64(3393.58), 'low': np.float64(3389.29), 'close': np.float64(3393.46), 'volume': 3157, 'ema_20': np.float64(3379.6951), 'rsi_14': np.float64(66.2), 'macd': np.float64(5.23771), 'macd_signal': np.float64(3.996219), 'rsi_25': np.float64(62.3), 'sma50_rsi25': np.float64(58.4), 'rsi_50': np.float64(59.3), 'sma25_rsi50': np.float64(56.1)}, {'time': '2025-08-27 06:00:00', 'open': np.float64(3390.66), 'high': np.float64(3392.01), 'low': np.float64(3388.8), 'close': np.float64(3391.13), 'volume': 3188, 'ema_20': np.float64(3380.78413), 'rsi_14': np.float64(63.2), 'macd': np.float64(5.502314), 'macd_signal': np.float64(4.297438), 'rsi_25': np.float64(60.8), 'sma50_rsi25': np.float64(58.3), 'rsi_50': np.float64(58.6), 'sma25_rsi50': np.float64(56.3)}, {'time': '2025-08-27 07:00:00', 'open': np.float64(3391.19), 'high': np.float64(3393.0), 'low': np.float64(3390.09), 'close': np.float64(3390.93), 'volume': 2962, 'ema_20': np.float64(3381.75041), 'rsi_14': np.float64(63.0), 'macd': np.float64(5.630966), 'macd_signal': np.float64(4.564143), 'rsi_25': np.float64(60.6), 'sma50_rsi25': np.float64(58.2), 'rsi_50': np.float64(58.5), 'sma25_rsi50': np.float64(56.4)}, {'time': '2025-08-27 08:00:00', 'open': np.float64(3390.9), 'high': np.float64(3393.51), 'low': np.float64(3389.02), 'close': np.float64(3389.62), 'volume': 6495, 'ema_20': np.float64(3382.49989), 'rsi_14': np.float64(61.2), 'macd': np.float64(5.56309), 'macd_signal': np.float64(4.763933), 'rsi_25': np.float64(59.7), 'sma50_rsi25': np.float64(58.1), 'rsi_50': np.float64(58.0), 'sma25_rsi50': np.float64(56.6)}, {'time': '2025-08-27 09:00:00', 'open': np.float64(3389.66), 'high': np.float64(3390.11), 'low': np.float64(3383.41), 'close': np.float64(3386.2), 'volume': 11316, 'ema_20': np.float64(3382.85228), 'rsi_14': np.float64(56.7), 'macd': np.float64(5.173693), 'macd_signal': np.float64(4.845885), 'rsi_25': np.float64(57.4), 'sma50_rsi25': np.float64(58.0), 'rsi_50': np.float64(56.9), 'sma25_rsi50': np.float64(56.8)}, {'time': '2025-08-27 10:00:00', 'open': np.float64(3386.14), 'high': np.float64(3386.95), 'low': np.float64(3382.16), 'close': np.float64(3384.11), 'volume': 6225, 'ema_20': np.float64(3382.97207), 'rsi_14': np.float64(54.0), 'macd': np.float64(4.642927), 'macd_signal': np.float64(4.805293), 'rsi_25': np.float64(56.0), 'sma50_rsi25': np.float64(57.8), 'rsi_50': np.float64(56.2), 'sma25_rsi50': np.float64(56.7)}, {'time': '2025-08-27 11:00:00', 'open': np.float64(3384.1), 'high': np.float64(3384.42), 'low': np.float64(3380.6), 'close': np.float64(3380.64), 'volume': 4966, 'ema_20': np.float64(3382.74996), 'rsi_14': np.float64(49.9), 'macd': np.float64(3.897364), 'macd_signal': np.float64(4.623707), 'rsi_25': np.float64(53.7), 'sma50_rsi25': np.float64(57.7), 'rsi_50': np.float64(55.0), 'sma25_rsi50': np.float64(56.6)}, {'time': '2025-08-27 12:00:00', 'open': np.float64(3380.65), 'high': np.float64(3381.21), 'low': np.float64(3373.93), 'close': np.float64(3374.94), 'volume': 7421, 'ema_20': np.float64(3382.00616), 'rsi_14': np.float64(44.0), 'macd': np.float64(2.814119), 'macd_signal': np.float64(4.26179), 'rsi_25': np.float64(50.2), 'sma50_rsi25': np.float64(57.5), 'rsi_50': np.float64(53.2), 'sma25_rsi50': np.float64(56.5)}, {'time': '2025-08-27 13:00:00', 'open': np.float64(3374.97), 'high': np.float64(3378.9), 'low': np.float64(3374.07), 'close': np.float64(3377.19), 'volume': 8190, 'ema_20': np.float64(3381.54748), 'rsi_14': np.float64(46.7), 'macd': np.float64(2.11284), 'macd_signal': np.float64(3.832), 'rsi_25': np.float64(51.5), 'sma50_rsi25': np.float64(57.3), 'rsi_50': np.float64(53.8), 'sma25_rsi50': np.float64(56.4)}, {'time': '2025-08-27 14:00:00', 'open': np.float64(3377.18), 'high': np.float64(3381.91), 'low': np.float64(3376.3), 'close': np.float64(3377.49), 'volume': 8498, 'ema_20': np.float64(3381.16105), 'rsi_14': np.float64(47.0), 'macd': np.float64(1.563258), 'macd_signal': np.float64(3.378251), 'rsi_25': np.float64(51.7), 'sma50_rsi25': np.float64(57.2), 'rsi_50': np.float64(53.9), 'sma25_rsi50': np.float64(56.3)}, {'time': '2025-08-27 15:00:00', 'open': np.float64(3377.48), 'high': np.float64(3381.6), 'low': np.float64(3373.78), 'close': np.float64(3381.48), 'volume': 8571, 'ema_20': np.float64(3381.19143), 'rsi_14': np.float64(51.8), 'macd': np.float64(1.43315), 'macd_signal': np.float64(2.989231), 'rsi_25': np.float64(54.0), 'sma50_rsi25': np.float64(57.1), 'rsi_50': np.float64(55.0), 'sma25_rsi50': np.float64(56.3)}, {'time': '2025-08-27 16:00:00', 'open': np.float64(3381.47), 'high': np.float64(3384.94), 'low': np.float64(3380.97), 'close': np.float64(3384.49), 'volume': 8584, 'ema_20': np.float64(3381.50558), 'rsi_14': np.float64(55.1), 'macd': np.float64(1.554995), 'macd_signal': np.float64(2.702384), 'rsi_25': np.float64(55.6), 'sma50_rsi25': np.float64(57.0), 'rsi_50': np.float64(55.8), 'sma25_rsi50': np.float64(56.3)}, {'time': '2025-08-27 17:00:00', 'open': np.float64(3384.47), 'high': np.float64(3385.43), 'low': np.float64(3377.65), 'close': np.float64(3378.23), 'volume': 7971, 'ema_20': np.float64(3381.19362), 'rsi_14': np.float64(47.8), 'macd': np.float64(1.133364), 'macd_signal': np.float64(2.38858), 'rsi_25': np.float64(51.6), 'sma50_rsi25': np.float64(56.8), 'rsi_50': np.float64(53.8), 'sma25_rsi50': np.float64(56.2)}, {'time': '2025-08-27 18:00:00', 'open': np.float64(3378.27), 'high': np.float64(3379.25), 'low': np.float64(3374.86), 'close': np.float64(3375.59), 'volume': 10437, 'ema_20': np.float64(3380.65994), 'rsi_14': np.float64(45.1), 'macd': np.float64(0.579512), 'macd_signal': np.float64(2.026766), 'rsi_25': np.float64(50.0), 'sma50_rsi25': np.float64(56.6), 'rsi_50': np.float64(53.0), 'sma25_rsi50': np.float64(56.0)}, {'time': '2025-08-27 19:00:00', 'open': np.float64(3375.66), 'high': np.float64(3378.18), 'low': np.float64(3373.83), 'close': np.float64(3376.78), 'volume': 8048, 'ema_20': np.float64(3380.29042), 'rsi_14': np.float64(46.6), 'macd': np.float64(0.233907), 'macd_signal': np.float64(1.668195), 'rsi_25': np.float64(50.8), 'sma50_rsi25': np.float64(56.5), 'rsi_50': np.float64(53.3), 'sma25_rsi50': np.float64(56.0)}, {'time': '2025-08-27 20:00:00', 'open': np.float64(3376.77), 'high': np.float64(3382.85), 'low': np.float64(3374.29), 'close': np.float64(3381.14), 'volume': 12025, 'ema_20': np.float64(3380.37133), 'rsi_14': np.float64(51.7), 'macd': np.float64(0.308275), 'macd_signal': np.float64(1.396211), 'rsi_25': np.float64(53.3), 'sma50_rsi25': np.float64(56.4), 'rsi_50': np.float64(54.5), 'sma25_rsi50': np.float64(56.0)}, {'time': '2025-08-27 21:00:00', 'open': np.float64(3381.16), 'high': np.float64(3384.84), 'low': np.float64(3375.55), 'close': np.float64(3377.88), 'volume': 18179, 'ema_20': np.float64(3380.13406), 'rsi_14': np.float64(48.0), 'macd': np.float64(0.10297), 'macd_signal': np.float64(1.137563), 'rsi_25': np.float64(51.2), 'sma50_rsi25': np.float64(56.2), 'rsi_50': np.float64(53.4), 'sma25_rsi50': np.float64(55.8)}, {'time': '2025-08-27 22:00:00', 'open': np.float64(3377.8), 'high': np.float64(3386.84), 'low': np.float64(3376.09), 'close': np.float64(3386.48), 'volume': 15125, 'ema_20': np.float64(3380.73844), 'rsi_14': np.float64(56.7), 'macd': np.float64(0.626985), 'macd_signal': np.float64(1.035447), 'rsi_25': np.float64(55.9), 'sma50_rsi25': np.float64(56.2), 'rsi_50': np.float64(55.7), 'sma25_rsi50': np.float64(55.9)}, {'time': '2025-08-27 23:00:00', 'open': np.float64(3386.52), 'high': np.float64(3390.4), 'low': np.float64(3385.17), 'close': np.float64(3388.0), 'volume': 12800, 'ema_20': np.float64(3381.43002), 'rsi_14': np.float64(58.1), 'macd': np.float64(1.151646), 'macd_signal': np.float64(1.058687), 'rsi_25': np.float64(56.7), 'sma50_rsi25': np.float64(56.1), 'rsi_50': np.float64(56.1), 'sma25_rsi50': np.float64(55.9)}, {'time': '2025-08-28 00:00:00', 'open': np.float64(3388.01), 'high': np.float64(3398.87), 'low': np.float64(3387.54), 'close': np.float64(3395.48), 'volume': 13584, 'ema_20': np.float64(3382.76811), 'rsi_14': np.float64(64.0), 'macd': np.float64(2.146277), 'macd_signal': np.float64(1.276205), 'rsi_25': np.float64(60.2), 'sma50_rsi25': np.float64(56.2), 'rsi_50': np.float64(58.0), 'sma25_rsi50': np.float64(56.0)}, {'time': '2025-08-28 01:00:00', 'open': np.float64(3395.47), 'high': np.float64(3397.47), 'low': np.float64(3394.93), 'close': np.float64(3395.19), 'volume': 9678, 'ema_20': np.float64(3383.95115), 'rsi_14': np.float64(63.6), 'macd': np.float64(2.877953), 'macd_signal': np.float64(1.596555), 'rsi_25': np.float64(60.0), 'sma50_rsi25': np.float64(56.2), 'rsi_50': np.float64(57.9), 'sma25_rsi50': np.float64(56.0)}, {'time': '2025-08-28 02:00:00', 'open': np.float64(3395.2), 'high': np.float64(3396.3), 'low': np.float64(3392.35), 'close': np.float64(3394.57), 'volume': 5773, 'ema_20': np.float64(3384.96247), 'rsi_14': np.float64(62.8), 'macd': np.float64(3.368948), 'macd_signal': np.float64(1.951033), 'rsi_25': np.float64(59.6), 'sma50_rsi25': np.float64(56.2), 'rsi_50': np.float64(57.7), 'sma25_rsi50': np.float64(56.0)}, {'time': '2025-08-28 03:00:00', 'open': np.float64(3394.6), 'high': np.float64(3396.41), 'low': np.float64(3393.82), 'close': np.float64(3396.26), 'volume': 7156, 'ema_20': np.float64(3386.03842), 'rsi_14': np.float64(64.2), 'macd': np.float64(3.850052), 'macd_signal': np.float64(2.330837), 'rsi_25': np.float64(60.4), 'sma50_rsi25': np.float64(56.2), 'rsi_50': np.float64(58.1), 'sma25_rsi50': np.float64(56.1)}, {'time': '2025-08-28 04:00:00', 'open': np.float64(3396.25), 'high': np.float64(3398.17), 'low': np.float64(3394.17), 'close': np.float64(3397.23), 'volume': 4575, 'ema_20': np.float64(3387.10429), 'rsi_14': np.float64(65.0), 'macd': np.float64(4.26049), 'macd_signal': np.float64(2.716768), 'rsi_25': np.float64(60.8), 'sma50_rsi25': np.float64(56.2), 'rsi_50': np.float64(58.3), 'sma25_rsi50': np.float64(56.1)}, {'time': '2025-08-28 06:00:00', 'open': np.float64(3397.96), 'high': np.float64(3398.41), 'low': np.float64(3394.86), 'close': np.float64(3395.79), 'volume': 2750, 'ema_20': np.float64(3387.9315), 'rsi_14': np.float64(62.7), 'macd': np.float64(4.418633), 'macd_signal': np.float64(3.057141), 'rsi_25': np.float64(59.7), 'sma50_rsi25': np.float64(56.3), 'rsi_50': np.float64(57.8), 'sma25_rsi50': np.float64(56.1)}, {'time': '2025-08-28 07:00:00', 'open': np.float64(3395.77), 'high': np.float64(3399.31), 'low': np.float64(3395.2), 'close': np.float64(3395.83), 'volume': 4053, 'ema_20': np.float64(3388.68374), 'rsi_14': np.float64(62.8), 'macd': np.float64(4.495371), 'macd_signal': np.float64(3.344787), 'rsi_25': np.float64(59.8), 'sma50_rsi25': np.float64(56.3), 'rsi_50': np.float64(57.8), 'sma25_rsi50': np.float64(56.0)}, {'time': '2025-08-28 08:00:00', 'open': np.float64(3395.82), 'high': np.float64(3398.04), 'low': np.float64(3391.34), 'close': np.float64(3392.07), 'volume': 6516, 'ema_20': np.float64(3389.00624), 'rsi_14': np.float64(56.7), 'macd': np.float64(4.204321), 'macd_signal': np.float64(3.516694), 'rsi_25': np.float64(56.9), 'sma50_rsi25': np.float64(56.3), 'rsi_50': np.float64(56.5), 'sma25_rsi50': np.float64(55.9)}, {'time': '2025-08-28 09:00:00', 'open': np.float64(3392.05), 'high': np.float64(3392.86), 'low': np.float64(3385.57), 'close': np.float64(3386.04), 'volume': 14520, 'ema_20': np.float64(3388.72374), 'rsi_14': np.float64(48.7), 'macd': np.float64(3.447352), 'macd_signal': np.float64(3.502825), 'rsi_25': np.float64(52.6), 'sma50_rsi25': np.float64(56.3), 'rsi_50': np.float64(54.4), 'sma25_rsi50': np.float64(55.8)}, {'time': '2025-08-28 10:00:00', 'open': np.float64(3386.0), 'high': np.float64(3391.39), 'low': np.float64(3384.58), 'close': np.float64(3391.38), 'volume': 10145, 'ema_20': np.float64(3388.97672), 'rsi_14': np.float64(54.8), 'macd': np.float64(3.240982), 'macd_signal': np.float64(3.450457), 'rsi_25': np.float64(55.7), 'sma50_rsi25': np.float64(56.4), 'rsi_50': np.float64(55.9), 'sma25_rsi50': np.float64(55.7)}, {'time': '2025-08-28 11:00:00', 'open': np.float64(3391.39), 'high': np.float64(3392.46), 'low': np.float64(3388.86), 'close': np.float64(3390.1), 'volume': 6882, 'ema_20': np.float64(3389.08369), 'rsi_14': np.float64(53.2), 'macd': np.float64(2.940253), 'macd_signal': np.float64(3.348416), 'rsi_25': np.float64(54.8), 'sma50_rsi25': np.float64(56.5), 'rsi_50': np.float64(55.5), 'sma25_rsi50': np.float64(55.6)}, {'time': '2025-08-28 12:00:00', 'open': np.float64(3390.08), 'high': np.float64(3392.21), 'low': np.float64(3388.72), 'close': np.float64(3390.32), 'volume': 4105, 'ema_20': np.float64(3389.20144), 'rsi_14': np.float64(53.4), 'macd': np.float64(2.688683), 'macd_signal': np.float64(3.216469), 'rsi_25': np.float64(54.9), 'sma50_rsi25': np.float64(56.4), 'rsi_50': np.float64(55.5), 'sma25_rsi50': np.float64(55.6)}, {'time': '2025-08-28 13:00:00', 'open': np.float64(3390.28), 'high': np.float64(3390.84), 'low': np.float64(3387.71), 'close': np.float64(3389.22), 'volume': 6892, 'ema_20': np.float64(3389.20321), 'rsi_14': np.float64(51.9), 'macd': np.float64(2.373194), 'macd_signal': np.float64(3.047814), 'rsi_25': np.float64(54.1), 'sma50_rsi25': np.float64(56.3), 'rsi_50': np.float64(55.1), 'sma25_rsi50': np.float64(55.6)}, {'time': '2025-08-28 14:00:00', 'open': np.float64(3389.23), 'high': np.float64(3395.61), 'low': np.float64(3388.85), 'close': np.float64(3395.34), 'volume': 12138, 'ema_20': np.float64(3389.78766), 'rsi_14': np.float64(59.0), 'macd': np.float64(2.587176), 'macd_signal': np.float64(2.955686), 'rsi_25': np.float64(57.7), 'sma50_rsi25': np.float64(56.3), 'rsi_50': np.float64(56.8), 'sma25_rsi50': np.float64(55.7)}, {'time': '2025-08-28 15:00:00', 'open': np.float64(3395.33), 'high': np.float64(3397.58), 'low': np.float64(3393.64), 'close': np.float64(3395.83), 'volume': 9807, 'ema_20': np.float64(3390.36312), 'rsi_14': np.float64(59.5), 'macd': np.float64(2.76443), 'macd_signal': np.float64(2.917435), 'rsi_25': np.float64(58.0), 'sma50_rsi25': np.float64(56.4), 'rsi_50': np.float64(57.0), 'sma25_rsi50': np.float64(55.9)}, {'time': '2025-08-28 16:00:00', 'open': np.float64(3395.85), 'high': np.float64(3401.0), 'low': np.float64(3393.91), 'close': np.float64(3400.66), 'volume': 11500, 'ema_20': np.float64(3391.34378), 'rsi_14': np.float64(64.2), 'macd': np.float64(3.257101), 'macd_signal': np.float64(2.985368), 'rsi_25': np.float64(60.6), 'sma50_rsi25': np.float64(56.4), 'rsi_50': np.float64(58.2), 'sma25_rsi50': np.float64(56.0)}, {'time': '2025-08-28 17:00:00', 'open': np.float64(3400.68), 'high': np.float64(3401.62), 'low': np.float64(3398.45), 'close': np.float64(3399.21), 'volume': 6791, 'ema_20': np.float64(3392.09294), 'rsi_14': np.float64(61.9), 'macd': np.float64(3.490309), 'macd_signal': np.float64(3.086356), 'rsi_25': np.float64(59.4), 'sma50_rsi25': np.float64(56.5), 'rsi_50': np.float64(57.7), 'sma25_rsi50': np.float64(56.2)}, {'time': '2025-08-28 18:00:00', 'open': np.float64(3399.24), 'high': np.float64(3399.24), 'low': np.float64(3393.86), 'close': np.float64(3397.03), 'volume': 12203, 'ema_20': np.float64(3392.56314), 'rsi_14': np.float64(58.4), 'macd': np.float64(3.459342), 'macd_signal': np.float64(3.160954), 'rsi_25': np.float64(57.7), 'sma50_rsi25': np.float64(56.5), 'rsi_50': np.float64(56.9), 'sma25_rsi50': np.float64(56.2)}, {'time': '2025-08-28 19:00:00', 'open': np.float64(3397.06), 'high': np.float64(3407.66), 'low': np.float64(3396.25), 'close': np.float64(3404.05), 'volume': 13167, 'ema_20': np.float64(3393.65712), 'rsi_14': np.float64(65.2), 'macd': np.float64(3.955659), 'macd_signal': np.float64(3.319895), 'rsi_25': np.float64(61.4), 'sma50_rsi25': np.float64(56.6), 'rsi_50': np.float64(58.8), 'sma25_rsi50': np.float64(56.4)}, {'time': '2025-08-28 20:00:00', 'open': np.float64(3404.04), 'high': np.float64(3408.71), 'low': np.float64(3396.85), 'close': np.float64(3397.22), 'volume': 21319, 'ema_20': np.float64(3393.99645), 'rsi_14': np.float64(55.7), 'macd': np.float64(3.754588), 'macd_signal': np.float64(3.406833), 'rsi_25': np.float64(56.4), 'sma50_rsi25': np.float64(56.6), 'rsi_50': np.float64(56.4), 'sma25_rsi50': np.float64(56.5)}, {'time': '2025-08-28 21:00:00', 'open': np.float64(3397.19), 'high': np.float64(3410.42), 'low': np.float64(3393.86), 'close': np.float64(3407.35), 'volume': 26024, 'ema_20': np.float64(3395.26821), 'rsi_14': np.float64(64.0), 'macd': np.float64(4.362358), 'macd_signal': np.float64(3.597938), 'rsi_25': np.float64(61.3), 'sma50_rsi25': np.float64(56.7), 'rsi_50': np.float64(58.9), 'sma25_rsi50': np.float64(56.8)}, {'time': '2025-08-28 22:00:00', 'open': np.float64(3407.36), 'high': np.float64(3413.47), 'low': np.float64(3406.47), 'close': np.float64(3412.23), 'volume': 20207, 'ema_20': np.float64(3396.88362), 'rsi_14': np.float64(67.2), 'macd': np.float64(5.178106), 'macd_signal': np.float64(3.913972), 'rsi_25': np.float64(63.4), 'sma50_rsi25': np.float64(56.9), 'rsi_50': np.float64(60.1), 'sma25_rsi50': np.float64(57.0)}, {'time': '2025-08-28 23:00:00', 'open': np.float64(3412.24), 'high': np.float64(3413.19), 'low': np.float64(3404.54), 'close': np.float64(3410.9), 'volume': 13985, 'ema_20': np.float64(3398.21851), 'rsi_14': np.float64(65.5), 'macd': np.float64(5.652118), 'macd_signal': np.float64(4.261601), 'rsi_25': np.float64(62.4), 'sma50_rsi25': np.float64(57.0), 'rsi_50': np.float64(59.6), 'sma25_rsi50': np.float64(57.2)}, {'time': '2025-08-29 00:00:00', 'open': np.float64(3411.0), 'high': np.float64(3417.74), 'low': np.float64(3409.94), 'close': np.float64(3415.52), 'volume': 12166, 'ema_20': np.float64(3399.86628), 'rsi_14': np.float64(68.5), 'macd': np.float64(6.32763), 'macd_signal': np.float64(4.674807), 'rsi_25': np.float64(64.4), 'sma50_rsi25': np.float64(57.3), 'rsi_50': np.float64(60.7), 'sma25_rsi50': np.float64(57.4)}, {'time': '2025-08-29 01:00:00', 'open': np.float64(3415.51), 'high': np.float64(3418.79), 'low': np.float64(3414.6), 'close': np.float64(3417.99), 'volume': 9886, 'ema_20': np.float64(3401.59234), 'rsi_14': np.float64(70.0), 'macd': np.float64(6.981804), 'macd_signal': np.float64(5.136206), 'rsi_25': np.float64(65.4), 'sma50_rsi25': np.float64(57.5), 'rsi_50': np.float64(61.2), 'sma25_rsi50': np.float64(57.6)}, {'time': '2025-08-29 02:00:00', 'open': np.float64(3418.0), 'high': np.float64(3420.37), 'low': np.float64(3417.36), 'close': np.float64(3420.07), 'volume': 8051, 'ema_20': np.float64(3403.35212), 'rsi_14': np.float64(71.3), 'macd': np.float64(7.580696), 'macd_signal': np.float64(5.625104), 'rsi_25': np.float64(66.2), 'sma50_rsi25': np.float64(57.6), 'rsi_50': np.float64(61.7), 'sma25_rsi50': np.float64(57.8)}, {'time': '2025-08-29 03:00:00', 'open': np.float64(3420.08), 'high': np.float64(3423.22), 'low': np.float64(3418.02), 'close': np.float64(3422.09), 'volume': 13193, 'ema_20': np.float64(3405.13668), 'rsi_14': np.float64(72.5), 'macd': np.float64(8.124662), 'macd_signal': np.float64(6.125016), 'rsi_25': np.float64(67.0), 'sma50_rsi25': np.float64(57.8), 'rsi_50': np.float64(62.2), 'sma25_rsi50': np.float64(58.0)}, {'time': '2025-08-29 04:00:00', 'open': np.float64(3422.0), 'high': np.float64(3422.36), 'low': np.float64(3416.37), 'close': np.float64(3417.11), 'volume': 4848, 'ema_20': np.float64(3406.277), 'rsi_14': np.float64(65.3), 'macd': np.float64(8.060993), 'macd_signal': np.float64(6.512211), 'rsi_25': np.float64(63.2), 'sma50_rsi25': np.float64(57.9), 'rsi_50': np.float64(60.4), 'sma25_rsi50': np.float64(58.1)}, {'time': '2025-08-29 06:00:00', 'open': np.float64(3416.92), 'high': np.float64(3417.84), 'low': np.float64(3415.57), 'close': np.float64(3416.81), 'volume': 2312, 'ema_20': np.float64(3407.28014), 'rsi_14': np.float64(64.8), 'macd': np.float64(7.895315), 'macd_signal': np.float64(6.788832), 'rsi_25': np.float64(62.9), 'sma50_rsi25': np.float64(58.0), 'rsi_50': np.float64(60.3), 'sma25_rsi50': np.float64(58.1)}, {'time': '2025-08-29 07:00:00', 'open': np.float64(3416.78), 'high': np.float64(3416.81), 'low': np.float64(3414.88), 'close': np.float64(3414.89), 'volume': 2592, 'ema_20': np.float64(3408.00489), 'rsi_14': np.float64(62.1), 'macd': np.float64(7.522373), 'macd_signal': np.float64(6.93554), 'rsi_25': np.float64(61.5), 'sma50_rsi25': np.float64(58.0), 'rsi_50': np.float64(59.6), 'sma25_rsi50': np.float64(58.2)}, {'time': '2025-08-29 08:00:00', 'open': np.float64(3414.88), 'high': np.float64(3417.88), 'low': np.float64(3413.1), 'close': np.float64(3413.47), 'volume': 7767, 'ema_20': np.float64(3408.52538), 'rsi_14': np.float64(60.1), 'macd': np.float64(7.031181), 'macd_signal': np.float64(6.954668), 'rsi_25': np.float64(60.4), 'sma50_rsi25': np.float64(58.0), 'rsi_50': np.float64(59.0), 'sma25_rsi50': np.float64(58.2)}, {'time': '2025-08-29 09:00:00', 'open': np.float64(3413.45), 'high': np.float64(3413.6), 'low': np.float64(3407.9), 'close': np.float64(3409.97), 'volume': 12707, 'ema_20': np.float64(3408.66296), 'rsi_14': np.float64(55.3), 'macd': np.float64(6.287014), 'macd_signal': np.float64(6.821137), 'rsi_25': np.float64(57.8), 'sma50_rsi25': np.float64(57.9), 'rsi_50': np.float64(57.8), 'sma25_rsi50': np.float64(58.2)}, {'time': '2025-08-29 10:00:00', 'open': np.float64(3409.93), 'high': np.float64(3413.94), 'low': np.float64(3409.93), 'close': np.float64(3411.27), 'volume': 7906, 'ema_20': np.float64(3408.91125), 'rsi_14': np.float64(56.7), 'macd': np.float64(5.736033), 'macd_signal': np.float64(6.604117), 'rsi_25': np.float64(58.5), 'sma50_rsi25': np.float64(57.9), 'rsi_50': np.float64(58.1), 'sma25_rsi50': np.float64(58.3)}, {'time': '2025-08-29 11:00:00', 'open': np.float64(3411.31), 'high': np.float64(3413.48), 'low': np.float64(3409.94), 'close': np.float64(3410.78), 'volume': 5775, 'ema_20': np.float64(3409.08922), 'rsi_14': np.float64(56.0), 'macd': np.float64(5.199898), 'macd_signal': np.float64(6.323273), 'rsi_25': np.float64(58.1), 'sma50_rsi25': np.float64(57.8), 'rsi_50': np.float64(58.0), 'sma25_rsi50': np.float64(58.5)}, {'time': '2025-08-29 12:00:00', 'open': np.float64(3410.67), 'high': np.float64(3411.32), 'low': np.float64(3407.52), 'close': np.float64(3408.83), 'volume': 5688, 'ema_20': np.float64(3409.06454), 'rsi_14': np.float64(53.2), 'macd': np.float64(4.565035), 'macd_signal': np.float64(5.971625), 'rsi_25': np.float64(56.6), 'sma50_rsi25': np.float64(57.8), 'rsi_50': np.float64(57.2), 'sma25_rsi50': np.float64(58.5)}, {'time': '2025-08-29 13:00:00', 'open': np.float64(3408.84), 'high': np.float64(3410.8), 'low': np.float64(3407.01), 'close': np.float64(3410.29), 'volume': 5942, 'ema_20': np.float64(3409.18125), 'rsi_14': np.float64(55.0), 'macd': np.float64(4.132079), 'macd_signal': np.float64(5.603716), 'rsi_25': np.float64(57.5), 'sma50_rsi25': np.float64(57.8), 'rsi_50': np.float64(57.6), 'sma25_rsi50': np.float64(58.6)}, {'time': '2025-08-29 14:00:00', 'open': np.float64(3410.31), 'high': np.float64(3412.61), 'low': np.float64(3407.85), 'close': np.float64(3410.19), 'volume': 9247, 'ema_20': np.float64(3409.27732), 'rsi_14': np.float64(54.8), 'macd': np.float64(3.737802), 'macd_signal': np.float64(5.230533), 'rsi_25': np.float64(57.4), 'sma50_rsi25': np.float64(57.8), 'rsi_50': np.float64(57.6), 'sma25_rsi50': np.float64(58.7)}, {'time': '2025-08-29 15:00:00', 'open': np.float64(3410.13), 'high': np.float64(3412.69), 'low': np.float64(3406.56), 'close': np.float64(3412.18), 'volume': 11134, 'ema_20': np.float64(3409.55376), 'rsi_14': np.float64(57.4), 'macd': np.float64(3.545046), 'macd_signal': np.float64(4.893436), 'rsi_25': np.float64(58.6), 'sma50_rsi25': np.float64(57.9), 'rsi_50': np.float64(58.2), 'sma25_rsi50': np.float64(58.8)}, {'time': '2025-08-29 16:00:00', 'open': np.float64(3412.24), 'high': np.float64(3414.81), 'low': np.float64(3409.71), 'close': np.float64(3410.42), 'volume': 9966, 'ema_20': np.float64(3409.63626), 'rsi_14': np.float64(54.4), 'macd': np.float64(3.213228), 'macd_signal': np.float64(4.557394), 'rsi_25': np.float64(57.1), 'sma50_rsi25': np.float64(58.0), 'rsi_50': np.float64(57.5), 'sma25_rsi50': np.float64(58.8)}, {'time': '2025-08-29 17:00:00', 'open': np.float64(3410.4), 'high': np.float64(3411.07), 'low': np.float64(3406.96), 'close': np.float64(3407.48), 'volume': 9600, 'ema_20': np.float64(3409.43091), 'rsi_14': np.float64(49.8), 'macd': np.float64(2.682109), 'macd_signal': np.float64(4.182337), 'rsi_25': np.float64(54.7), 'sma50_rsi25': np.float64(58.1), 'rsi_50': np.float64(56.4), 'sma25_rsi50': np.float64(58.8)}, {'time': '2025-08-29 18:00:00', 'open': np.float64(3407.47), 'high': np.float64(3408.91), 'low': np.float64(3404.62), 'close': np.float64(3405.49), 'volume': 7884, 'ema_20': np.float64(3409.05558), 'rsi_14': np.float64(46.8), 'macd': np.float64(2.076678), 'macd_signal': np.float64(3.761205), 'rsi_25': np.float64(53.1), 'sma50_rsi25': np.float64(58.1), 'rsi_50': np.float64(55.6), 'sma25_rsi50': np.float64(58.7)}, {'time': '2025-08-29 19:00:00', 'open': np.float64(3405.47), 'high': np.float64(3411.32), 'low': np.float64(3404.3), 'close': np.float64(3410.76), 'volume': 9232, 'ema_20': np.float64(3409.21791), 'rsi_14': np.float64(54.5), 'macd': np.float64(1.999071), 'macd_signal': np.float64(3.408779), 'rsi_25': np.float64(56.6), 'sma50_rsi25': np.float64(58.2), 'rsi_50': np.float64(57.1), 'sma25_rsi50': np.float64(58.7)}, {'time': '2025-08-29 20:00:00', 'open': np.float64(3410.74), 'high': np.float64(3416.85), 'low': np.float64(3406.71), 'close': np.float64(3416.46), 'volume': 16789, 'ema_20': np.float64(3409.90763), 'rsi_14': np.float64(61.0), 'macd': np.float64(2.370187), 'macd_signal': np.float64(3.20106), 'rsi_25': np.float64(59.9), 'sma50_rsi25': np.float64(58.3), 'rsi_50': np.float64(58.7), 'sma25_rsi50': np.float64(58.7)}, {'time': '2025-08-29 21:00:00', 'open': np.float64(3416.49), 'high': np.float64(3429.04), 'low': np.float64(3413.57), 'close': np.float64(3428.93), 'volume': 24435, 'ema_20': np.float64(3411.71928), 'rsi_14': np.float64(70.9), 'macd': np.float64(3.628694), 'macd_signal': np.float64(3.286587), 'rsi_25': np.float64(65.9), 'sma50_rsi25': np.float64(58.6), 'rsi_50': np.float64(61.8), 'sma25_rsi50': np.float64(58.9)}, {'time': '2025-08-29 22:00:00', 'open': np.float64(3428.77), 'high': np.float64(3441.13), 'low': np.float64(3428.66), 'close': np.float64(3441.01), 'volume': 30856, 'ema_20': np.float64(3414.50888), 'rsi_14': np.float64(77.0), 'macd': np.float64(5.536999), 'macd_signal': np.float64(3.736669), 'rsi_25': np.float64(70.4), 'sma50_rsi25': np.float64(59.0), 'rsi_50': np.float64(64.5), 'sma25_rsi50': np.float64(59.2)}, {'time': '2025-08-29 23:00:00', 'open': np.float64(3441.02), 'high': np.float64(3447.7), 'low': np.float64(3438.99), 'close': np.float64(3445.47), 'volume': 23310, 'ema_20': np.float64(3417.45755), 'rsi_14': np.float64(78.7), 'macd': np.float64(7.324793), 'macd_signal': np.float64(4.454294), 'rsi_25': np.float64(71.8), 'sma50_rsi25': np.float64(59.4), 'rsi_50': np.float64(65.4), 'sma25_rsi50': np.float64(59.5)}, {'time': '2025-08-30 00:00:00', 'open': np.float64(3445.46), 'high': np.float64(3449.33), 'low': np.float64(3442.23), 'close': np.float64(3449.15), 'volume': 17393, 'ema_20': np.float64(3420.47588), 'rsi_14': np.float64(80.1), 'macd': np.float64(8.935574), 'macd_signal': np.float64(5.35055), 'rsi_25': np.float64(72.9), 'sma50_rsi25': np.float64(59.8), 'rsi_50': np.float64(66.1), 'sma25_rsi50': np.float64(59.7)}, {'time': '2025-08-30 01:00:00', 'open': np.float64(3449.17), 'high': np.float64(3450.14), 'low': np.float64(3440.95), 'close': np.float64(3443.64), 'volume': 15021, 'ema_20': np.float64(3422.68199), 'rsi_14': np.float64(72.6), 'macd': np.float64(9.656208), 'macd_signal': np.float64(6.211682), 'rsi_25': np.float64(68.7), 'sma50_rsi25': np.float64(60.1), 'rsi_50': np.float64(64.1), 'sma25_rsi50': np.float64(59.9)}, {'time': '2025-08-30 02:00:00', 'open': np.float64(3443.67), 'high': np.float64(3445.65), 'low': np.float64(3440.94), 'close': np.float64(3443.01), 'volume': 10270, 'ema_20': np.float64(3424.61799), 'rsi_14': np.float64(71.8), 'macd': np.float64(10.060508), 'macd_signal': np.float64(6.981447), 'rsi_25': np.float64(68.2), 'sma50_rsi25': np.float64(60.4), 'rsi_50': np.float64(63.8), 'sma25_rsi50': np.float64(60.0)}, {'time': '2025-08-30 03:00:00', 'open': np.float64(3442.89), 'high': np.float64(3454.04), 'low': np.float64(3441.63), 'close': np.float64(3452.78), 'volume': 14441, 'ema_20': np.float64(3427.30009), 'rsi_14': np.float64(76.3), 'macd': np.float64(11.041991), 'macd_signal': np.float64(7.793556), 'rsi_25': np.float64(71.4), 'sma50_rsi25': np.float64(60.7), 'rsi_50': np.float64(65.8), 'sma25_rsi50': np.float64(60.2)}, {'time': '2025-08-30 04:00:00', 'open': np.float64(3452.73), 'high': np.float64(3452.8), 'low': np.float64(3445.72), 'close': np.float64(3448.15), 'volume': 8749, 'ema_20': np.float64(3429.28579), 'rsi_14': np.float64(70.5), 'macd': np.float64(11.31578), 'macd_signal': np.float64(8.498001), 'rsi_25': np.float64(68.0), 'sma50_rsi25': np.float64(60.8), 'rsi_50': np.float64(64.1), 'sma25_rsi50': np.float64(60.3)}]}\n\n\nAdditional Instructions: I bias BUY side on XAUUSD", "image_analyzed": false, "use_signal_format": 1, "timestamp": "2025-08-30T17:58:04.053088"}